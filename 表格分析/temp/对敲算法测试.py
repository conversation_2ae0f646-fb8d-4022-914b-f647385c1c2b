#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对敲检测算法大规模测试 - 5000次测试，1U-50万U交易量
"""

import random
import numpy as np
from 双因子对敲检测算法 import calculate_hedge_score

def generate_test_cases(num_cases=5000):
    """
    生成测试用例
    
    Returns:
        list: [(profit_a, profit_b, order_a, order_b, expected_label, case_type), ...]
    """
    test_cases = []
    
    for i in range(num_cases):
        # 随机生成交易量：1U到50万U
        order_a = random.uniform(1, 500000)
        order_b = random.uniform(1, 500000)
        
        # 随机选择测试类型
        case_type = random.choice([
            'hedge_perfect',      # 完美对敲
            'hedge_near',         # 接近对敲  
            'hedge_same_side',    # 同边对敲
            'normal_opposite',    # 正常异边交易
            'normal_same',        # 正常同边交易
            'zero_profit'         # 零盈亏
        ])
        
        if case_type == 'hedge_perfect':
            # 完美对敲：完全抵消
            base_profit = random.uniform(-order_a * 0.1, order_a * 0.1)
            profit_a = base_profit
            profit_b = -base_profit
            expected_label = 'hedge'
            
        elif case_type == 'hedge_near':
            # 接近对敲：接近抵消，误差在5%以内
            base_profit = random.uniform(-order_a * 0.1, order_a * 0.1)
            error = random.uniform(-abs(base_profit) * 0.05, abs(base_profit) * 0.05)
            profit_a = base_profit
            profit_b = -base_profit + error
            expected_label = 'hedge'
            
        elif case_type == 'hedge_same_side':
            # 同边对敲：一方接近0，另一方正常盈亏
            if random.choice([True, False]):
                # A接近0，B正常
                profit_a = random.uniform(-order_a * 0.005, order_a * 0.005)  # ±0.5%
                profit_b = random.uniform(-order_b * 0.1, order_b * 0.1)      # ±10%
                # 确保同边
                if profit_a * profit_b < 0:
                    profit_b = -profit_b
            else:
                # B接近0，A正常
                profit_b = random.uniform(-order_b * 0.005, order_b * 0.005)  # ±0.5%
                profit_a = random.uniform(-order_a * 0.1, order_a * 0.1)      # ±10%
                # 确保同边
                if profit_a * profit_b < 0:
                    profit_a = -profit_a
            expected_label = 'hedge'
            
        elif case_type == 'normal_opposite':
            # 正常异边交易：一盈一亏，但不抵消
            profit_a = random.uniform(order_a * 0.02, order_a * 0.2)   # 2%-20%盈利
            profit_b = random.uniform(-order_b * 0.2, -order_b * 0.02) # 2%-20%亏损
            # 确保不接近抵消
            total = abs(profit_a + profit_b)
            profit_sum = abs(profit_a) + abs(profit_b)
            if profit_sum > 0 and total / profit_sum < 0.3:  # 如果太接近抵消
                profit_b = profit_b * 0.3  # 减少亏损，避免抵消
            expected_label = 'normal'
            
        elif case_type == 'normal_same':
            # 正常同边交易：都盈利或都亏损，且都有正常幅度
            side = random.choice([1, -1])
            profit_a = side * random.uniform(order_a * 0.02, order_a * 0.2)  # 2%-20%
            profit_b = side * random.uniform(order_b * 0.02, order_b * 0.2)  # 2%-20%
            expected_label = 'normal'
            
        elif case_type == 'zero_profit':
            # 零盈亏情况
            profit_a = 0
            profit_b = 0
            expected_label = 'hedge'  # 零盈亏也算对敲特征
        
        test_cases.append((profit_a, profit_b, order_a, order_b, expected_label, case_type))
    
    return test_cases

def evaluate_accuracy(test_cases, threshold=0.5):
    """
    评估算法准确度
    
    Args:
        test_cases: 测试用例列表
        threshold: 判断阈值，超过此值认为是对敲
    
    Returns:
        dict: 评估结果
    """
    results = {
        'total': len(test_cases),
        'correct': 0,
        'false_positive': 0,  # 误报：正常交易被判为对敲
        'false_negative': 0,  # 漏报：对敲被判为正常交易
        'true_positive': 0,   # 正确识别对敲
        'true_negative': 0,   # 正确识别正常交易
        'by_type': {},
        'score_distribution': {'hedge': [], 'normal': []}
    }
    
    for profit_a, profit_b, order_a, order_b, expected_label, case_type in test_cases:
        score = calculate_hedge_score(profit_a, profit_b, order_a, order_b)
        predicted_label = 'hedge' if score >= threshold else 'normal'
        
        # 记录评分分布
        results['score_distribution'][expected_label].append(score)
        
        # 统计各类型结果
        if case_type not in results['by_type']:
            results['by_type'][case_type] = {'total': 0, 'correct': 0, 'avg_score': []}
        
        results['by_type'][case_type]['total'] += 1
        results['by_type'][case_type]['avg_score'].append(score)
        
        # 判断正确性
        is_correct = (predicted_label == expected_label)
        
        if is_correct:
            results['correct'] += 1
            results['by_type'][case_type]['correct'] += 1
            
            if expected_label == 'hedge':
                results['true_positive'] += 1
            else:
                results['true_negative'] += 1
        else:
            if predicted_label == 'hedge' and expected_label == 'normal':
                results['false_positive'] += 1
            elif predicted_label == 'normal' and expected_label == 'hedge':
                results['false_negative'] += 1
    
    # 计算平均评分
    for case_type in results['by_type']:
        scores = results['by_type'][case_type]['avg_score']
        results['by_type'][case_type]['avg_score'] = np.mean(scores)
    
    return results

def print_results(results):
    """打印测试结果"""
    total = results['total']
    accuracy = results['correct'] / total * 100
    
    print("=" * 60)
    print("对敲检测算法测试结果 (5000次测试)")
    print("=" * 60)
    print(f"总测试数量: {total}")
    print(f"整体准确率: {accuracy:.2f}%")
    print()
    
    # 混淆矩阵
    print("混淆矩阵:")
    print(f"  真正例 (正确识别对敲): {results['true_positive']}")
    print(f"  真负例 (正确识别正常): {results['true_negative']}")
    print(f"  假正例 (误报对敲): {results['false_positive']}")
    print(f"  假负例 (漏报对敲): {results['false_negative']}")
    print()
    
    # 精确率和召回率
    if results['true_positive'] + results['false_positive'] > 0:
        precision = results['true_positive'] / (results['true_positive'] + results['false_positive'])
        print(f"精确率 (Precision): {precision:.3f}")
    
    if results['true_positive'] + results['false_negative'] > 0:
        recall = results['true_positive'] / (results['true_positive'] + results['false_negative'])
        print(f"召回率 (Recall): {recall:.3f}")
    print()
    
    # 各类型详细结果
    print("各类型测试结果:")
    print("-" * 60)
    for case_type, data in results['by_type'].items():
        accuracy_type = data['correct'] / data['total'] * 100
        print(f"{case_type:20s}: {accuracy_type:6.2f}% ({data['correct']:4d}/{data['total']:4d}) "
              f"平均评分: {data['avg_score']:.3f}")
    print()
    
    # 评分分布
    print("评分分布:")
    hedge_scores = results['score_distribution']['hedge']
    normal_scores = results['score_distribution']['normal']
    
    if hedge_scores:
        print(f"对敲类评分 - 平均: {np.mean(hedge_scores):.3f}, "
              f"中位数: {np.median(hedge_scores):.3f}, "
              f"标准差: {np.std(hedge_scores):.3f}")
    
    if normal_scores:
        print(f"正常类评分 - 平均: {np.mean(normal_scores):.3f}, "
              f"中位数: {np.median(normal_scores):.3f}, "
              f"标准差: {np.std(normal_scores):.3f}")

if __name__ == "__main__":
    print("开始生成5000个测试用例...")
    test_cases = generate_test_cases(5000)
    
    print("开始测试算法准确度...")
    results = evaluate_accuracy(test_cases, threshold=0.5)
    
    print_results(results)
    
    # 测试不同阈值的效果
    print("\n" + "=" * 60)
    print("不同阈值下的准确率:")
    print("=" * 60)
    for threshold in [0.3, 0.4, 0.5, 0.6, 0.7, 0.8]:
        results_thresh = evaluate_accuracy(test_cases, threshold=threshold)
        accuracy = results_thresh['correct'] / results_thresh['total'] * 100
        precision = results_thresh['true_positive'] / (results_thresh['true_positive'] + results_thresh['false_positive']) if (results_thresh['true_positive'] + results_thresh['false_positive']) > 0 else 0
        recall = results_thresh['true_positive'] / (results_thresh['true_positive'] + results_thresh['false_negative']) if (results_thresh['true_positive'] + results_thresh['false_negative']) > 0 else 0
        print(f"阈值 {threshold:.1f}: 准确率 {accuracy:.2f}%, 精确率 {precision:.3f}, 召回率 {recall:.3f}")
