#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数调整对比测试 - 测试不同参数组合的准确率
"""

import random
import math

def calculate_hedge_score_v1(profit_a, profit_b, expected_scale=None):
    """原版算法：接近0(40%) + 相对波动率(60%)"""
    
    profit_sum = abs(profit_a) + abs(profit_b)
    if profit_sum == 0:
        return 0.0
    
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    
    if not is_same_side:
        total_profit = profit_a + profit_b
        return 1.0 - abs(total_profit) / profit_sum
    else:
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        max_abs = max(abs_a, abs_b)
        
        if expected_scale and expected_scale > 0:
            scale = expected_scale
        else:
            if max_abs <= 1:
                scale = 10.0
            elif max_abs <= 10:
                scale = 100.0
            elif max_abs <= 100:
                scale = 1000.0
            else:
                scale = max_abs * 10
        
        if min_abs <= 0.1:
            closeness_score = 0.9
        elif min_abs <= 1:
            closeness_score = 0.8
        elif min_abs <= 5:
            closeness_score = 0.6
        elif min_abs <= 10:
            closeness_score = 0.4
        else:
            closeness_score = 0.1 if max_abs >= 20 else 0.2
        
        max_relative = max(abs_a / scale, abs_b / scale)
        if max_relative <= 0.01:
            volatility_score = 0.9
        elif max_relative <= 0.05:
            volatility_score = 0.8
        elif max_relative <= 0.1:
            volatility_score = 0.6
        elif max_relative <= 0.2:
            volatility_score = 0.4
        else:
            volatility_score = 0.2
        
        return 0.4 * closeness_score + 0.6 * volatility_score


def calculate_hedge_score_v2(profit_a, profit_b, expected_scale=None):
    """调整版算法：接近0(50%) + 相对波动率(50%)"""
    
    profit_sum = abs(profit_a) + abs(profit_b)
    if profit_sum == 0:
        return 0.0
    
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    
    if not is_same_side:
        total_profit = profit_a + profit_b
        return 1.0 - abs(total_profit) / profit_sum
    else:
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        max_abs = max(abs_a, abs_b)
        
        if expected_scale and expected_scale > 0:
            scale = expected_scale
        else:
            if max_abs <= 1:
                scale = 10.0
            elif max_abs <= 10:
                scale = 100.0
            elif max_abs <= 100:
                scale = 1000.0
            else:
                scale = max_abs * 10
        
        if min_abs <= 0.1:
            closeness_score = 0.9
        elif min_abs <= 1:
            closeness_score = 0.8
        elif min_abs <= 5:
            closeness_score = 0.6
        elif min_abs <= 10:
            closeness_score = 0.4
        else:
            closeness_score = 0.1 if max_abs >= 20 else 0.2
        
        max_relative = max(abs_a / scale, abs_b / scale)
        if max_relative <= 0.01:
            volatility_score = 0.9
        elif max_relative <= 0.05:
            volatility_score = 0.8
        elif max_relative <= 0.1:
            volatility_score = 0.6
        elif max_relative <= 0.2:
            volatility_score = 0.4
        else:
            volatility_score = 0.2
        
        return 0.5 * closeness_score + 0.5 * volatility_score


def calculate_hedge_score_v3(profit_a, profit_b, expected_scale=None):
    """调整版算法：接近0(60%) + 相对波动率(40%)"""
    
    profit_sum = abs(profit_a) + abs(profit_b)
    if profit_sum == 0:
        return 0.0
    
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    
    if not is_same_side:
        total_profit = profit_a + profit_b
        return 1.0 - abs(total_profit) / profit_sum
    else:
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        max_abs = max(abs_a, abs_b)
        
        if expected_scale and expected_scale > 0:
            scale = expected_scale
        else:
            if max_abs <= 1:
                scale = 10.0
            elif max_abs <= 10:
                scale = 100.0
            elif max_abs <= 100:
                scale = 1000.0
            else:
                scale = max_abs * 10
        
        # 放宽接近0的阈值
        if min_abs <= 0.1:
            closeness_score = 0.9
        elif min_abs <= 1:
            closeness_score = 0.8
        elif min_abs <= 5:
            closeness_score = 0.6
        elif min_abs <= 15:  # 从10调整到15
            closeness_score = 0.4
        elif min_abs <= 30:  # 从20调整到30
            closeness_score = 0.2
        else:
            closeness_score = 0.1
        
        max_relative = max(abs_a / scale, abs_b / scale)
        if max_relative <= 0.01:
            volatility_score = 0.9
        elif max_relative <= 0.05:
            volatility_score = 0.8
        elif max_relative <= 0.1:
            volatility_score = 0.6
        elif max_relative <= 0.2:
            volatility_score = 0.4
        else:
            volatility_score = 0.2
        
        return 0.6 * closeness_score + 0.4 * volatility_score


def generate_test_cases():
    """生成标准测试用例"""
    
    test_cases = []
    scales = [10, 100, 1000, 10000, 100000]
    
    case_id = 1
    
    for scale in scales:
        # 异边高风险对敲（应该得高分）
        for _ in range(10):
            profit_a = random.uniform(0.1 * scale, 0.3 * scale)
            profit_b = -random.uniform(0.8 * profit_a, 1.2 * profit_a)
            test_cases.append((case_id, profit_a, profit_b, scale, "异边高风险", "高风险对敲"))
            case_id += 1
        
        # 同边高风险对敲（一方接近0，应该得高分）
        for _ in range(10):
            close_to_zero = random.uniform(0.001 * scale, 0.01 * scale)
            far_from_zero = random.uniform(0.05 * scale, 0.2 * scale)
            test_cases.append((case_id, -close_to_zero, -far_from_zero, scale, "同边高风险", "高风险对敲"))
            case_id += 1
        
        # 正常交易（都远离0，应该得低分）
        for _ in range(10):
            loss_a = random.uniform(0.2 * scale, 0.5 * scale)
            loss_b = random.uniform(0.2 * scale, 0.5 * scale)
            test_cases.append((case_id, -loss_a, -loss_b, scale, "正常交易", "正常交易"))
            case_id += 1
    
    return test_cases


def evaluate_accuracy(algorithm_func, test_cases, algorithm_name):
    """评估算法准确率"""
    
    correct_predictions = 0
    total_cases = len(test_cases)
    
    type_stats = {}
    
    for case_id, profit_a, profit_b, scale, expected_type, expected_risk in test_cases:
        score = algorithm_func(profit_a, profit_b, scale)
        
        # 确定预测的风险等级
        if score >= 0.8:
            predicted_risk = "高风险对敲"
        elif score >= 0.6:
            predicted_risk = "中等风险对敲"
        elif score >= 0.4:
            predicted_risk = "低风险对敲"
        else:
            predicted_risk = "正常交易"
        
        # 判断是否预测正确
        is_correct = False
        if expected_risk == "高风险对敲" and predicted_risk in ["高风险对敲", "中等风险对敲"]:
            is_correct = True
        elif expected_risk == "正常交易" and predicted_risk in ["正常交易", "低风险对敲"]:
            is_correct = True
        
        if is_correct:
            correct_predictions += 1
        
        # 统计各类型的表现
        if expected_type not in type_stats:
            type_stats[expected_type] = {"total": 0, "correct": 0, "scores": []}
        
        type_stats[expected_type]["total"] += 1
        type_stats[expected_type]["scores"].append(score)
        if is_correct:
            type_stats[expected_type]["correct"] += 1
    
    # 计算总体准确率
    overall_accuracy = correct_predictions / total_cases * 100
    
    print(f"\n=== {algorithm_name} 测试结果 ===")
    print(f"总体准确率: {overall_accuracy:.1f}% ({correct_predictions}/{total_cases})")
    
    print("\n各类型表现:")
    for type_name, stats in type_stats.items():
        accuracy = stats["correct"] / stats["total"] * 100
        avg_score = sum(stats["scores"]) / len(stats["scores"])
        print(f"  {type_name:<12}: 准确率{accuracy:>5.1f}% ({stats['correct']:>2d}/{stats['total']:>2d}), 平均评分{avg_score:.3f}")
    
    return overall_accuracy, type_stats


def run_comparison_test():
    """运行对比测试"""
    
    print("=== 参数调整对比测试 ===")
    print("生成测试用例...")
    
    # 生成测试用例
    test_cases = generate_test_cases()
    print(f"生成 {len(test_cases)} 个测试用例")
    
    # 测试三种算法
    algorithms = [
        (calculate_hedge_score_v1, "原版算法 (40%接近0 + 60%波动率)"),
        (calculate_hedge_score_v2, "调整版1 (50%接近0 + 50%波动率)"),
        (calculate_hedge_score_v3, "调整版2 (60%接近0 + 40%波动率 + 放宽阈值)")
    ]
    
    results = []
    
    for algorithm_func, algorithm_name in algorithms:
        accuracy, type_stats = evaluate_accuracy(algorithm_func, test_cases, algorithm_name)
        results.append((algorithm_name, accuracy, type_stats))
    
    # 对比总结
    print("\n" + "="*60)
    print("=== 算法对比总结 ===")
    
    print("\n总体准确率对比:")
    for algorithm_name, accuracy, _ in results:
        print(f"  {algorithm_name:<35}: {accuracy:>5.1f}%")
    
    # 找出最佳算法
    best_algorithm = max(results, key=lambda x: x[1])
    print(f"\n最佳算法: {best_algorithm[0]} (准确率: {best_algorithm[1]:.1f}%)")
    
    # 详细对比各类型表现
    print("\n各类型详细对比:")
    type_names = ["异边高风险", "同边高风险", "正常交易"]
    
    for type_name in type_names:
        print(f"\n{type_name}:")
        for algorithm_name, _, type_stats in results:
            if type_name in type_stats:
                stats = type_stats[type_name]
                accuracy = stats["correct"] / stats["total"] * 100
                avg_score = sum(stats["scores"]) / len(stats["scores"])
                print(f"  {algorithm_name:<35}: 准确率{accuracy:>5.1f}%, 平均评分{avg_score:.3f}")


if __name__ == "__main__":
    run_comparison_test()
