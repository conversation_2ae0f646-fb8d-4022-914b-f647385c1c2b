#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合优化对敲检测测试 - 包含所有优化方案
1. 调整权重比例：接近0(60%) + 相对波动率(40%)
2. 放宽接近0阈值
3. 改进规模推测
4. 增加差异度因子
5. 动态权重调整
6. 针对同边情况调整风险等级阈值
"""

import random
import math

def calculate_hedge_score_comprehensive(profit_a, profit_b, expected_scale=None):
    """
    综合优化的对敲检测算法
    包含所有优化方案
    """
    
    # 基础检查
    profit_sum = abs(profit_a) + abs(profit_b)
    if profit_sum == 0:
        return 0.0, {"risk_level": "正常交易", "explanation": "无盈亏"}
    
    # 判断是否同边
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    
    if not is_same_side:
        # 异边（一盈一亏）：使用原始公式
        total_profit = profit_a + profit_b
        score = 1.0 - abs(total_profit) / profit_sum
        case_type = "异边"
    else:
        # 同边（双盈/双亏）：综合优化算法
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        max_abs = max(abs_a, abs_b)
        
        # 改进的规模推测逻辑
        if expected_scale and expected_scale > 0:
            scale = expected_scale
        else:
            total_abs = abs_a + abs_b
            if total_abs <= 2:
                scale = 10.0
            elif total_abs <= 20:
                scale = 100.0
            elif total_abs <= 200:
                scale = 1000.0
            elif total_abs <= 2000:
                scale = 10000.0
            else:
                scale = total_abs * 5
        
        # 因子1: 优化的接近0程度评分（放宽阈值）
        if min_abs <= 0.2:
            closeness_score = 0.9
        elif min_abs <= 2:
            closeness_score = 0.8
        elif min_abs <= 8:
            closeness_score = 0.6
        elif min_abs <= 15:
            closeness_score = 0.4
        elif min_abs <= 30:
            closeness_score = 0.2
        else:
            closeness_score = 0.1
        
        # 因子2: 相对波动率评分
        max_relative = max(abs_a / scale, abs_b / scale)
        if max_relative <= 0.01:
            volatility_score = 0.9
        elif max_relative <= 0.05:
            volatility_score = 0.8
        elif max_relative <= 0.1:
            volatility_score = 0.6
        elif max_relative <= 0.2:
            volatility_score = 0.4
        else:
            volatility_score = 0.2
        
        # 因子3: 差异度评分（新增）
        diff_ratio = abs(abs_a - abs_b) / max_abs
        if diff_ratio >= 0.8:
            diff_score = 0.9
        elif diff_ratio >= 0.5:
            diff_score = 0.7
        elif diff_ratio >= 0.2:
            diff_score = 0.5
        else:
            diff_score = 0.3
        
        # 动态权重调整
        if min_abs <= 1:
            # 确实有一方很接近0，提高接近0的权重
            score = 0.7 * closeness_score + 0.2 * volatility_score + 0.1 * diff_score
        elif min_abs <= 5:
            # 一方比较接近0，平衡权重
            score = 0.5 * closeness_score + 0.3 * volatility_score + 0.2 * diff_score
        else:
            # 都不够接近0，提高差异度和波动率权重
            score = 0.2 * closeness_score + 0.5 * volatility_score + 0.3 * diff_score
        
        case_type = "双盈" if profit_a > 0 else "双亏"
    
    # 针对不同情况调整风险等级阈值
    if case_type in ["双盈", "双亏"]:
        # 同边情况：降低阈值
        if score >= 0.5:
            risk_level = "高风险对敲"
        elif score >= 0.35:
            risk_level = "中等风险对敲"
        elif score >= 0.25:
            risk_level = "低风险对敲"
        else:
            risk_level = "正常交易"
    else:
        # 异边情况：保持原阈值
        if score >= 0.8:
            risk_level = "高风险对敲"
        elif score >= 0.6:
            risk_level = "中等风险对敲"
        elif score >= 0.4:
            risk_level = "低风险对敲"
        else:
            risk_level = "正常交易"
    
    details = {
        "score": score,
        "risk_level": risk_level,
        "case_type": case_type,
        "is_same_side": is_same_side
    }
    
    return score, details


def calculate_hedge_score_original(profit_a, profit_b, expected_scale=None):
    """原始算法（用于对比）"""
    
    profit_sum = abs(profit_a) + abs(profit_b)
    if profit_sum == 0:
        return 0.0, {"risk_level": "正常交易"}
    
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    
    if not is_same_side:
        total_profit = profit_a + profit_b
        score = 1.0 - abs(total_profit) / profit_sum
    else:
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        max_abs = max(abs_a, abs_b)
        
        if expected_scale and expected_scale > 0:
            scale = expected_scale
        else:
            if max_abs <= 1:
                scale = 10.0
            elif max_abs <= 10:
                scale = 100.0
            elif max_abs <= 100:
                scale = 1000.0
            else:
                scale = max_abs * 10
        
        if min_abs <= 0.1:
            closeness_score = 0.9
        elif min_abs <= 1:
            closeness_score = 0.8
        elif min_abs <= 5:
            closeness_score = 0.6
        elif min_abs <= 10:
            closeness_score = 0.4
        else:
            closeness_score = 0.1 if max_abs >= 20 else 0.2
        
        max_relative = max(abs_a / scale, abs_b / scale)
        if max_relative <= 0.01:
            volatility_score = 0.9
        elif max_relative <= 0.05:
            volatility_score = 0.8
        elif max_relative <= 0.1:
            volatility_score = 0.6
        elif max_relative <= 0.2:
            volatility_score = 0.4
        else:
            volatility_score = 0.2
        
        score = 0.4 * closeness_score + 0.6 * volatility_score
    
    # 统一阈值
    if score >= 0.8:
        risk_level = "高风险对敲"
    elif score >= 0.6:
        risk_level = "中等风险对敲"
    elif score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    return score, {"risk_level": risk_level}


def generate_test_cases():
    """生成1000个测试用例"""
    
    test_cases = []
    scales = [10, 100, 1000, 10000, 100000, 500000]
    case_id = 1
    
    for scale in scales:
        # 异边情况
        for _ in range(15):
            profit_a = random.uniform(0.1 * scale, 0.3 * scale)
            profit_b = -random.uniform(0.8 * profit_a, 1.2 * profit_a)
            test_cases.append((case_id, profit_a, profit_b, scale, "异边-高风险"))
            case_id += 1
            
            profit_a = random.uniform(0.1 * scale, 0.2 * scale)
            profit_b = -random.uniform(0.5 * profit_a, 0.8 * profit_a)
            test_cases.append((case_id, profit_a, profit_b, scale, "异边-中等风险"))
            case_id += 1
        
        # 同边双亏情况
        for _ in range(20):
            close_to_zero = random.uniform(0.001 * scale, 0.02 * scale)
            far_from_zero = random.uniform(0.05 * scale, 0.2 * scale)
            test_cases.append((case_id, -close_to_zero, -far_from_zero, scale, "同边双亏-高风险"))
            case_id += 1
            
            close_to_zero = random.uniform(0.02 * scale, 0.08 * scale)
            far_from_zero = random.uniform(0.1 * scale, 0.3 * scale)
            test_cases.append((case_id, -close_to_zero, -far_from_zero, scale, "同边双亏-中等风险"))
            case_id += 1
            
            loss_a = random.uniform(0.2 * scale, 0.5 * scale)
            loss_b = random.uniform(0.2 * scale, 0.5 * scale)
            test_cases.append((case_id, -loss_a, -loss_b, scale, "同边双亏-正常交易"))
            case_id += 1
        
        # 同边双盈情况
        for _ in range(20):
            close_to_zero = random.uniform(0.001 * scale, 0.02 * scale)
            far_from_zero = random.uniform(0.05 * scale, 0.2 * scale)
            test_cases.append((case_id, close_to_zero, far_from_zero, scale, "同边双盈-高风险"))
            case_id += 1
            
            close_to_zero = random.uniform(0.02 * scale, 0.08 * scale)
            far_from_zero = random.uniform(0.1 * scale, 0.3 * scale)
            test_cases.append((case_id, close_to_zero, far_from_zero, scale, "同边双盈-中等风险"))
            case_id += 1
            
            profit_a = random.uniform(0.2 * scale, 0.5 * scale)
            profit_b = random.uniform(0.2 * scale, 0.5 * scale)
            test_cases.append((case_id, profit_a, profit_b, scale, "同边双盈-正常交易"))
            case_id += 1
    
    return test_cases


def run_comprehensive_test():
    """运行综合优化测试"""
    
    print("=== 综合优化对敲检测算法测试 ===")
    print("包含所有优化方案：权重调整、阈值放宽、差异度因子、动态权重、分级阈值")
    
    test_cases = generate_test_cases()
    print(f"生成 {len(test_cases)} 个测试用例\n")
    
    original_results = []
    optimized_results = []
    
    for case_id, profit_a, profit_b, scale, expected_type in test_cases:
        original_score, original_details = calculate_hedge_score_original(profit_a, profit_b, scale)
        optimized_score, optimized_details = calculate_hedge_score_comprehensive(profit_a, profit_b, scale)
        
        original_results.append((case_id, profit_a, profit_b, scale, expected_type, original_score, original_details["risk_level"]))
        optimized_results.append((case_id, profit_a, profit_b, scale, expected_type, optimized_score, optimized_details["risk_level"]))
    
    # 分析结果
    analyze_comprehensive_results(original_results, optimized_results)


def analyze_comprehensive_results(original_results, optimized_results):
    """分析综合优化结果"""
    
    print("=== 综合优化效果分析 ===\n")
    
    # 按预期类型分组统计
    type_stats = {}
    
    for i, (case_id, profit_a, profit_b, scale, expected_type, _, _) in enumerate(original_results):
        if expected_type not in type_stats:
            type_stats[expected_type] = {
                'count': 0,
                'original_scores': [],
                'optimized_scores': [],
                'original_high_risk': 0,
                'optimized_high_risk': 0,
                'original_avg': 0,
                'optimized_avg': 0,
                'improvement': 0
            }
        
        type_stats[expected_type]['count'] += 1
        type_stats[expected_type]['original_scores'].append(original_results[i][5])
        type_stats[expected_type]['optimized_scores'].append(optimized_results[i][5])
        
        # 统计高风险识别
        if original_results[i][6] == "高风险对敲":
            type_stats[expected_type]['original_high_risk'] += 1
        if optimized_results[i][6] == "高风险对敲":
            type_stats[expected_type]['optimized_high_risk'] += 1
    
    # 计算统计数据
    for type_name in type_stats:
        stats = type_stats[type_name]
        stats['original_avg'] = sum(stats['original_scores']) / len(stats['original_scores'])
        stats['optimized_avg'] = sum(stats['optimized_scores']) / len(stats['optimized_scores'])
        stats['improvement'] = stats['optimized_avg'] - stats['original_avg']
    
    # 输出详细对比
    print("详细对比结果：")
    print(f"{'类型':<20} {'案例数':<6} {'原始平均':<8} {'优化平均':<8} {'改进':<8} {'原始高风险率':<12} {'优化高风险率':<12}")
    print("-" * 90)
    
    for type_name in sorted(type_stats.keys()):
        stats = type_stats[type_name]
        original_rate = f"{stats['original_high_risk']}/{stats['count']} ({stats['original_high_risk']/stats['count']*100:.0f}%)"
        optimized_rate = f"{stats['optimized_high_risk']}/{stats['count']} ({stats['optimized_high_risk']/stats['count']*100:.0f}%)"
        
        print(f"{type_name:<20} {stats['count']:<6} {stats['original_avg']:<8.3f} {stats['optimized_avg']:<8.3f} "
              f"{stats['improvement']:+.3f}   {original_rate:<12} {optimized_rate:<12}")
    
    # 重点分析同边高风险对敲
    print("\n=== 重点：同边高风险对敲优化效果 ===")
    same_side_high = [k for k in type_stats.keys() if "高风险" in k and "同边" in k]
    
    total_original_high = 0
    total_optimized_high = 0
    total_same_side_high = 0
    
    for type_name in same_side_high:
        stats = type_stats[type_name]
        total_original_high += stats['original_high_risk']
        total_optimized_high += stats['optimized_high_risk']
        total_same_side_high += stats['count']
        
        print(f"\n{type_name}:")
        print(f"  评分提升: {stats['original_avg']:.3f} → {stats['optimized_avg']:.3f} ({stats['improvement']:+.3f})")
        print(f"  高风险识别: {stats['original_high_risk']}/{stats['count']} → {stats['optimized_high_risk']}/{stats['count']} "
              f"({stats['original_high_risk']/stats['count']*100:.0f}% → {stats['optimized_high_risk']/stats['count']*100:.0f}%)")
    
    print(f"\n同边高风险对敲总体改进:")
    print(f"  原始算法高风险识别率: {total_original_high}/{total_same_side_high} ({total_original_high/total_same_side_high*100:.1f}%)")
    print(f"  优化算法高风险识别率: {total_optimized_high}/{total_same_side_high} ({total_optimized_high/total_same_side_high*100:.1f}%)")
    print(f"  识别率提升: {(total_optimized_high-total_original_high)/total_same_side_high*100:+.1f}个百分点")


if __name__ == "__main__":
    run_comprehensive_test()
