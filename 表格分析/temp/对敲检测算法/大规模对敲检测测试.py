#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大规模对敲检测测试 - 1000对情况，从10U到50万U
"""

import random
import math

def calculate_hedge_score(profit_a, profit_b, expected_scale=None):
    """核心对敲检测算法"""
    
    # 基础检查
    profit_sum = abs(profit_a) + abs(profit_b)
    if profit_sum == 0:
        return 0.0
    
    # 判断是否同边
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    
    if not is_same_side:
        # 异边（一盈一亏）：使用原始公式
        total_profit = profit_a + profit_b
        return 1.0 - abs(total_profit) / profit_sum
    
    else:
        # 同边（双盈/双亏）：接近0(40%) + 相对波动率(60%)
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        max_abs = max(abs_a, abs_b)
        
        # 推测交易规模
        if expected_scale and expected_scale > 0:
            scale = expected_scale
        else:
            if max_abs <= 1:
                scale = 10.0
            elif max_abs <= 10:
                scale = 100.0
            elif max_abs <= 100:
                scale = 1000.0
            else:
                scale = max_abs * 10
        
        # closeness_score: 接近0程度评分
        if min_abs <= 0.1:
            closeness_score = 0.9
        elif min_abs <= 1:
            closeness_score = 0.8
        elif min_abs <= 5:
            closeness_score = 0.6
        elif min_abs <= 10:
            closeness_score = 0.4
        else:
            closeness_score = 0.1 if max_abs >= 20 else 0.2
        
        # volatility_score: 相对波动率评分
        max_relative = max(abs_a / scale, abs_b / scale)
        if max_relative <= 0.01:
            volatility_score = 0.9
        elif max_relative <= 0.05:
            volatility_score = 0.8
        elif max_relative <= 0.1:
            volatility_score = 0.6
        elif max_relative <= 0.2:
            volatility_score = 0.4
        else:
            volatility_score = 0.2
        
        # 综合评分：接近0(50%) + 相对波动率(50%)
        return 0.5 * closeness_score + 0.5 * volatility_score


def generate_test_cases():
    """生成1000对测试用例，覆盖10U到50万U的规模"""
    
    test_cases = []
    
    # 规模范围：10U, 100U, 1000U, 1万U, 10万U, 50万U
    scales = [10, 100, 1000, 10000, 100000, 500000]
    
    case_id = 1
    
    for scale in scales:
        # 每个规模生成不同类型的测试用例
        
        # === 1. 异边情况（一盈一亏）===
        for _ in range(20):
            # 高风险对敲：接近抵消
            profit_a = random.uniform(0.1 * scale, 0.3 * scale)
            profit_b = -random.uniform(0.8 * profit_a, 1.2 * profit_a)
            test_cases.append((case_id, profit_a, profit_b, scale, "异边-高风险"))
            case_id += 1
            
            # 中等风险对敲：部分抵消
            profit_a = random.uniform(0.1 * scale, 0.2 * scale)
            profit_b = -random.uniform(0.5 * profit_a, 0.8 * profit_a)
            test_cases.append((case_id, profit_a, profit_b, scale, "异边-中等风险"))
            case_id += 1
        
        # === 2. 同边双亏情况 ===
        for _ in range(30):
            # 高风险对敲：一方很接近0
            close_to_zero = random.uniform(0.001 * scale, 0.01 * scale)
            far_from_zero = random.uniform(0.05 * scale, 0.2 * scale)
            test_cases.append((case_id, -close_to_zero, -far_from_zero, scale, "同边双亏-高风险"))
            case_id += 1
            
            # 中等风险对敲：一方接近0
            close_to_zero = random.uniform(0.01 * scale, 0.05 * scale)
            far_from_zero = random.uniform(0.1 * scale, 0.3 * scale)
            test_cases.append((case_id, -close_to_zero, -far_from_zero, scale, "同边双亏-中等风险"))
            case_id += 1
            
            # 正常交易：都远离0
            loss_a = random.uniform(0.2 * scale, 0.5 * scale)
            loss_b = random.uniform(0.2 * scale, 0.5 * scale)
            test_cases.append((case_id, -loss_a, -loss_b, scale, "同边双亏-正常交易"))
            case_id += 1
        
        # === 3. 同边双盈情况 ===
        for _ in range(30):
            # 高风险对敲：一方很接近0
            close_to_zero = random.uniform(0.001 * scale, 0.01 * scale)
            far_from_zero = random.uniform(0.05 * scale, 0.2 * scale)
            test_cases.append((case_id, close_to_zero, far_from_zero, scale, "同边双盈-高风险"))
            case_id += 1
            
            # 中等风险对敲：一方接近0
            close_to_zero = random.uniform(0.01 * scale, 0.05 * scale)
            far_from_zero = random.uniform(0.1 * scale, 0.3 * scale)
            test_cases.append((case_id, close_to_zero, far_from_zero, scale, "同边双盈-中等风险"))
            case_id += 1
            
            # 正常交易：都远离0
            profit_a = random.uniform(0.2 * scale, 0.5 * scale)
            profit_b = random.uniform(0.2 * scale, 0.5 * scale)
            test_cases.append((case_id, profit_a, profit_b, scale, "同边双盈-正常交易"))
            case_id += 1
    
    return test_cases


def analyze_results(results):
    """分析测试结果"""
    
    print("\n=== 测试结果统计分析 ===")
    
    # 按规模分组统计
    scale_stats = {}
    type_stats = {}
    risk_stats = {"高风险对敲": 0, "中等风险对敲": 0, "低风险对敲": 0, "正常交易": 0}
    
    for case_id, profit_a, profit_b, scale, expected_type, score, actual_risk in results:
        # 规模统计
        if scale not in scale_stats:
            scale_stats[scale] = {"count": 0, "avg_score": 0, "scores": []}
        scale_stats[scale]["count"] += 1
        scale_stats[scale]["scores"].append(score)
        
        # 类型统计
        if expected_type not in type_stats:
            type_stats[expected_type] = {"count": 0, "avg_score": 0, "scores": []}
        type_stats[expected_type]["count"] += 1
        type_stats[expected_type]["scores"].append(score)
        
        # 风险等级统计
        risk_stats[actual_risk] += 1
    
    # 计算平均分
    for scale in scale_stats:
        scale_stats[scale]["avg_score"] = sum(scale_stats[scale]["scores"]) / len(scale_stats[scale]["scores"])
    
    for type_name in type_stats:
        type_stats[type_name]["avg_score"] = sum(type_stats[type_name]["scores"]) / len(type_stats[type_name]["scores"])
    
    # 输出统计结果
    print("\n按交易规模统计：")
    for scale in sorted(scale_stats.keys()):
        stats = scale_stats[scale]
        print(f"  {scale:>7}U: {stats['count']:>3}个案例, 平均评分: {stats['avg_score']:.3f}")
    
    print("\n按预期类型统计：")
    for type_name in sorted(type_stats.keys()):
        stats = type_stats[type_name]
        print(f"  {type_name:<15}: {stats['count']:>3}个案例, 平均评分: {stats['avg_score']:.3f}")
    
    print("\n按风险等级统计：")
    total_cases = sum(risk_stats.values())
    for risk_level, count in risk_stats.items():
        percentage = count / total_cases * 100
        print(f"  {risk_level:<10}: {count:>3}个案例 ({percentage:>5.1f}%)")
    
    print(f"\n总计: {total_cases} 个测试案例")


def run_large_scale_test():
    """运行大规模测试"""
    
    print("=== 大规模对敲检测测试 ===")
    print("生成1000对测试用例，覆盖10U到50万U规模...")
    
    # 生成测试用例
    test_cases = generate_test_cases()
    
    print(f"实际生成 {len(test_cases)} 个测试用例")
    print("\n开始测试...")
    
    results = []
    
    for case_id, profit_a, profit_b, scale, expected_type in test_cases:
        score = calculate_hedge_score(profit_a, profit_b, scale)
        
        # 确定风险等级
        if score >= 0.8:
            risk_level = "高风险对敲"
        elif score >= 0.6:
            risk_level = "中等风险对敲"
        elif score >= 0.4:
            risk_level = "低风险对敲"
        else:
            risk_level = "正常交易"
        
        results.append((case_id, profit_a, profit_b, scale, expected_type, score, risk_level))
    
    # 分析结果
    analyze_results(results)
    
    # 显示一些典型案例
    print("\n=== 典型案例展示 ===")
    
    # 按评分排序，显示最高分和最低分的案例
    results_sorted = sorted(results, key=lambda x: x[5], reverse=True)
    
    print("\n最高评分案例（前10个）：")
    for i, (case_id, profit_a, profit_b, scale, expected_type, score, risk_level) in enumerate(results_sorted[:10]):
        print(f"  {i+1:2d}. 规模{scale:>7}U: A={profit_a:>8.3f}, B={profit_b:>8.3f}, 评分:{score:.3f} ({risk_level})")
    
    print("\n最低评分案例（后10个）：")
    for i, (case_id, profit_a, profit_b, scale, expected_type, score, risk_level) in enumerate(results_sorted[-10:]):
        print(f"  {i+1:2d}. 规模{scale:>7}U: A={profit_a:>8.3f}, B={profit_b:>8.3f}, 评分:{score:.3f} ({risk_level})")


if __name__ == "__main__":
    run_large_scale_test()
