#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分情况全面对敲检测算法
一盈一亏：原始公式足够
双盈/双亏：额外补充，考虑仓位大小和相对波动率
"""

from dataclasses import dataclass
from typing import Tuple, Optional
import math

@dataclass
class Position:
    """仓位信息"""
    real_profit: float  # 实际盈亏
    volume: float = 0   # 交易量/交易规模
    direction: str = '' # 方向

def calculate_situational_hedge_score(pos_a: Position, pos_b: Position, 
                                    expected_scale: float = None) -> Tuple[float, dict]:
    """
    分情况全面对敲检测算法
    
    核心思路：
    1. 一盈一亏：使用原始公式，已经足够准确
    2. 双盈/双亏：使用增强公式，考虑仓位大小和相对波动率
    
    Args:
        pos_a: 仓位A
        pos_b: 仓位B  
        expected_scale: 预期交易规模
    
    Returns:
        Tuple[float, dict]: (最终评分, 详细信息)
    """
    
    # === 基础计算 ===
    total_profit = pos_a.real_profit + pos_b.real_profit
    profit_sum = abs(pos_a.real_profit) + abs(pos_b.real_profit)
    
    if profit_sum == 0:
        return 0.0, {
            'base_score': 0.0,
            'final_score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'case_type': '无盈亏'
        }
    
    # 基础评分（原始公式）
    base_score = 1.0 - abs(total_profit) / profit_sum
    
    pos_a_profit = pos_a.real_profit
    pos_b_profit = pos_b.real_profit
    
    # === 分情况处理 ===
    if (pos_a_profit > 0 and pos_b_profit < 0) or (pos_a_profit < 0 and pos_b_profit > 0):
        # 一盈一亏：原始公式足够
        case_type = "一盈一亏"
        final_score = base_score
        
        if base_score >= 0.9:
            explanation = f"一盈一亏且几乎完全抵消(评分:{base_score:.3f})，对敲特征明显"
        elif base_score >= 0.7:
            explanation = f"一盈一亏且大部分抵消(评分:{base_score:.3f})，有对敲特征"
        else:
            explanation = f"一盈一亏但抵消程度有限(评分:{base_score:.3f})，对敲特征一般"
            
        details = {
            'base_score': base_score,
            'final_score': final_score,
            'explanation': explanation,
            'case_type': case_type,
            'method': '原始公式'
        }
        
    else:
        # 双盈或双亏：使用增强算法
        if pos_a_profit > 0 and pos_b_profit > 0:
            case_type = "双盈"
        else:
            case_type = "双亏"
            
        final_score, details = _calculate_enhanced_double_score(
            pos_a_profit, pos_b_profit, expected_scale, case_type, base_score
        )
    
    # 风险等级
    if details['final_score'] >= 0.8:
        risk_level = "高风险对敲"
    elif details['final_score'] >= 0.6:
        risk_level = "中等风险对敲"
    elif details['final_score'] >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    details['risk_level'] = risk_level
    details['total_profit'] = total_profit
    details['profit_sum'] = profit_sum
    
    return final_score, details


def _calculate_enhanced_double_score(profit_a, profit_b, expected_scale, case_type, base_score):
    """
    计算双盈/双亏的增强评分
    
    考虑因素：
    1. 仓位大小（绝对金额）
    2. 相对波动率（相对于交易规模）
    3. 金额接近程度
    4. 总和接近0程度
    """
    
    abs_a = abs(profit_a)
    abs_b = abs(profit_b)
    
    # === 推测交易规模 ===
    if expected_scale and expected_scale > 0:
        scale = expected_scale
    else:
        # 根据盈亏金额智能推测规模
        max_profit = max(abs_a, abs_b)
        if max_profit <= 1:
            scale = 10.0
        elif max_profit <= 10:
            scale = 100.0
        elif max_profit <= 100:
            scale = 1000.0
        else:
            scale = max_profit * 10
    
    # === 因子1: 仓位大小评分 ===
    # 使用指数衰减：金额越小评分越高
    max_abs = max(abs_a, abs_b)
    size_decay_factor = 10.0  # 衰减参数
    size_score = math.exp(-max_abs / size_decay_factor)
    
    # === 因子2: 相对波动率评分 ===
    rel_a = abs_a / scale
    rel_b = abs_b / scale
    avg_relative = (rel_a + rel_b) / 2
    
    # 相对波动率越小评分越高
    volatility_decay_factor = 0.05  # 5%作为衰减参数
    volatility_score = math.exp(-avg_relative / volatility_decay_factor)
    
    # === 因子3: 金额接近程度评分 ===
    if min(abs_a, abs_b) > 0:
        ratio = max(abs_a, abs_b) / min(abs_a, abs_b)
        # 比例越接近1评分越高
        ratio_score = math.exp(-(ratio - 1) / 2.0)
    else:
        ratio_score = 0.0
    
    # === 因子4: 总和接近0程度评分 ===
    total_abs = abs(profit_a + profit_b)
    total_sum = abs_a + abs_b
    if total_sum > 0:
        # 总和相对于个体金额越小越好
        total_closeness = 1.0 - (total_abs / total_sum)
        total_score = total_closeness
    else:
        total_score = 0.0
    
    # === 综合评分策略 ===
    # 根据不同情况调整权重
    
    if max_abs <= 5:
        # 小金额情况：重点关注相对波动和接近程度
        w1, w2, w3, w4 = 0.2, 0.4, 0.2, 0.2  # 仓位大小, 相对波动, 接近程度, 总和接近0
        strategy = "小金额策略"
    elif max_abs <= 20:
        # 中等金额情况：平衡各因素
        w1, w2, w3, w4 = 0.3, 0.3, 0.2, 0.2
        strategy = "中等金额策略"
    else:
        # 大金额情况：重点关注仓位大小控制
        w1, w2, w3, w4 = 0.5, 0.2, 0.15, 0.15
        strategy = "大金额策略"
    
    # 加权计算最终评分
    enhanced_score = (w1 * size_score + w2 * volatility_score + 
                     w3 * ratio_score + w4 * total_score)
    
    # 确保评分在合理范围内
    final_score = max(0.0, min(1.0, enhanced_score))
    
    # 详细说明
    explanation = (f"{case_type}增强算法({strategy}): "
                  f"仓位大小{size_score:.3f} + 相对波动{volatility_score:.3f} + "
                  f"接近程度{ratio_score:.3f} + 总和接近0{total_score:.3f} = {final_score:.3f}")
    
    details = {
        'base_score': base_score,
        'final_score': final_score,
        'explanation': explanation,
        'case_type': case_type,
        'method': '增强算法',
        'strategy': strategy,
        'factors': {
            'size_score': size_score,
            'volatility_score': volatility_score,
            'ratio_score': ratio_score,
            'total_score': total_score,
            'max_abs': max_abs,
            'avg_relative': avg_relative,
            'scale': scale,
            'weights': [w1, w2, w3, w4]
        }
    }
    
    return final_score, details


def test_situational_algorithm():
    """测试分情况算法"""
    
    print("=== 分情况全面对敲检测算法测试 ===\n")
    print("策略:")
    print("1. 一盈一亏：使用原始公式")
    print("2. 双盈/双亏：使用增强算法，考虑仓位大小和相对波动率\n")
    
    test_cases = [
        # 一盈一亏情况（使用原始公式）
        (Position(10.0), Position(-9.5), 100.0, "一盈一亏-几乎抵消"),
        (Position(10.0), Position(-8.0), 100.0, "一盈一亏-部分抵消"),
        (Position(10.0), Position(-5.0), 100.0, "一盈一亏-抵消有限"),
        (Position(1.0), Position(-0.9), 100.0, "一盈一亏-小金额"),
        
        # 双亏情况（使用增强算法）
        (Position(-0.03), Position(-1.0), 10.0, "双亏-你的例子(10U规模)"),
        (Position(-0.03), Position(-1.0), 100.0, "双亏-你的例子(100U规模)"),
        (Position(-2.0), Position(-3.0), 100.0, "双亏-小金额相近"),
        (Position(-1.0), Position(-10.0), 100.0, "双亏-小金额差距大"),
        (Position(-20.0), Position(-25.0), 100.0, "双亏-中等金额"),
        (Position(-50.0), Position(-55.0), 100.0, "双亏-大金额"),
        
        # 双盈情况（使用增强算法）
        (Position(2.0), Position(3.0), 100.0, "双盈-小金额相近"),
        (Position(1.0), Position(10.0), 100.0, "双盈-小金额差距大"),
        (Position(20.0), Position(25.0), 100.0, "双盈-中等金额"),
        (Position(50.0), Position(55.0), 100.0, "双盈-大金额"),
        
        # 特殊情况
        (Position(-0.1), Position(-0.1), 100.0, "双亏-完全相同"),
        (Position(0.5), Position(0.5), 100.0, "双盈-完全相同"),
    ]
    
    for i, (pos_a, pos_b, scale, description) in enumerate(test_cases, 1):
        score, details = calculate_situational_hedge_score(pos_a, pos_b, scale)
        
        print(f"测试用例 {i}: {description}")
        print(f"仓位: A={pos_a.real_profit}, B={pos_b.real_profit} (规模{scale}U)")
        print(f"情况类型: {details['case_type']}")
        print(f"使用方法: {details['method']}")
        
        if 'strategy' in details:
            print(f"策略: {details['strategy']}")
            factors = details['factors']
            print(f"详细因子: 仓位大小{factors['size_score']:.3f}, 相对波动{factors['volatility_score']:.3f}, "
                  f"接近程度{factors['ratio_score']:.3f}, 总和接近0{factors['total_score']:.3f}")
        
        print(f"基础评分: {details['base_score']:.3f}")
        print(f"最终评分: {details['final_score']:.3f}")
        print(f"风险等级: {details['risk_level']}")
        print(f"说明: {details['explanation']}")
        print("-" * 80)


if __name__ == "__main__":
    test_situational_algorithm()
