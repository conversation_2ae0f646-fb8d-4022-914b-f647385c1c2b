#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理不同规模仓位的对敲检测算法
解决一方小金额(-1U)，另一方大金额(-101U)的对敲识别问题
"""

def calculate_hedge_score_with_scale(pos_a_profit, pos_b_profit, pos_a_volume=None, pos_b_volume=None):
    """
    考虑仓位规模的对敲检测算法
    
    核心思想：
    1. 一盈一亏：保持原逻辑
    2. 双盈/双亏：考虑每个仓位相对于其规模的"接近0"程度
    
    Args:
        pos_a_profit: 仓位A的实际盈亏
        pos_b_profit: 仓位B的实际盈亏
        pos_a_volume: 仓位A的交易量（用于估算仓位规模）
        pos_b_volume: 仓位B的交易量（用于估算仓位规模）
    
    Returns:
        dict: 包含评分和详细信息
    """
    
    # === 基础计算 ===
    total_profit = pos_a_profit + pos_b_profit
    profit_sum = abs(pos_a_profit) + abs(pos_b_profit)
    
    if profit_sum == 0:
        return {
            'score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'case_type': '无盈亏'
        }
    
    # 基础对敲评分
    base_score = 1.0 - abs(total_profit) / profit_sum
    
    # === 改进的逻辑：考虑仓位规模 ===
    
    if pos_a_profit > 0 and pos_b_profit > 0:
        # 双盈情况
        case_type = "双盈"
        adjustment_factor, explanation = _calculate_same_sign_adjustment(
            pos_a_profit, pos_b_profit, pos_a_volume, pos_b_volume, "盈利"
        )
        
    elif pos_a_profit < 0 and pos_b_profit < 0:
        # 双亏情况
        case_type = "双亏"
        adjustment_factor, explanation = _calculate_same_sign_adjustment(
            pos_a_profit, pos_b_profit, pos_a_volume, pos_b_volume, "亏损"
        )
        
    else:
        # 一盈一亏情况：保持原逻辑
        case_type = "一盈一亏"
        
        if abs(total_profit) < min(abs(pos_a_profit), abs(pos_b_profit)) * 0.1:
            adjustment_factor = 1.2
            explanation = f"一盈一亏且几乎完全抵消，对敲特征明显"
        else:
            adjustment_factor = 1.0
            explanation = f"一盈一亏，符合对敲特征"
    
    # 最终评分
    final_score = min(base_score * adjustment_factor, 1.0)
    
    # 风险等级
    if final_score >= 0.8:
        risk_level = "高风险对敲"
    elif final_score >= 0.6:
        risk_level = "中等风险对敲"
    elif final_score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    return {
        'score': final_score,
        'base_score': base_score,
        'adjustment_factor': adjustment_factor,
        'explanation': explanation,
        'case_type': case_type,
        'risk_level': risk_level,
        'total_profit': total_profit,
        'profit_sum': profit_sum
    }


def _calculate_same_sign_adjustment(pos_a_profit, pos_b_profit, pos_a_volume, pos_b_volume, profit_type):
    """
    计算同号盈亏的调整因子
    
    策略：
    1. 如果有交易量信息，计算每个仓位的盈亏率
    2. 如果没有交易量信息，使用绝对金额的相对比较
    3. 判断两个仓位是否都"接近0"
    """
    
    if pos_a_volume and pos_b_volume and pos_a_volume > 0 and pos_b_volume > 0:
        # 方法1：基于交易量计算盈亏率
        profit_rate_a = abs(pos_a_profit) / pos_a_volume  # 每单位交易量的盈亏
        profit_rate_b = abs(pos_b_profit) / pos_b_volume
        
        # 判断两个盈亏率是否都很小（接近0）
        max_rate = max(profit_rate_a, profit_rate_b)
        
        if max_rate <= 0.01:  # 盈亏率都≤1%
            adjustment_factor = 0.9
            explanation = f"双{profit_type}且盈亏率都很小(≤1%)，有对敲可能 (A:{pos_a_profit:.1f}/{pos_a_volume}={profit_rate_a:.1%}, B:{pos_b_profit:.1f}/{pos_b_volume}={profit_rate_b:.1%})"
        elif max_rate <= 0.05:  # 盈亏率都≤5%
            adjustment_factor = 0.6
            explanation = f"双{profit_type}且盈亏率较小(≤5%)，中等对敲可能 (A:{profit_rate_a:.1%}, B:{profit_rate_b:.1%})"
        else:
            adjustment_factor = 0.3
            explanation = f"双{profit_type}且盈亏率较大(>{profit_type}%)，对敲可能性较低 (A:{profit_rate_a:.1%}, B:{profit_rate_b:.1%})"
            
    else:
        # 方法2：基于绝对金额的智能判断
        abs_a = abs(pos_a_profit)
        abs_b = abs(pos_b_profit)
        
        # 检查是否存在"小金额+大金额但比例合理"的情况
        # 例如：-1U 和 -101U，比例约为1:100
        ratio = max(abs_a, abs_b) / min(abs_a, abs_b) if min(abs_a, abs_b) > 0 else float('inf')
        
        if ratio <= 2:  # 金额相近（比例≤2:1）
            if max(abs_a, abs_b) <= 10:
                adjustment_factor = 0.9
                explanation = f"双{profit_type}且金额都很小且相近，有对敲可能 (A:{pos_a_profit:.1f}, B:{pos_b_profit:.1f})"
            else:
                adjustment_factor = 0.6
                explanation = f"双{profit_type}且金额相近，中等对敲可能 (A:{pos_a_profit:.1f}, B:{pos_b_profit:.1f})"
                
        elif 10 <= ratio <= 200:  # 合理的比例差距（如1U vs 101U）
            # 检查小金额是否接近0
            min_amount = min(abs_a, abs_b)
            if min_amount <= 5:  # 小金额≤5U
                adjustment_factor = 0.7
                explanation = f"双{profit_type}且存在小金额接近0的合理比例，有对敲可能 (A:{pos_a_profit:.1f}, B:{pos_b_profit:.1f}, 比例:{ratio:.0f}:1)"
            else:
                adjustment_factor = 0.4
                explanation = f"双{profit_type}且比例合理但金额不够小，低对敲可能 (A:{pos_a_profit:.1f}, B:{pos_b_profit:.1f})"
                
        else:  # 比例差距过大
            adjustment_factor = 0.2
            explanation = f"双{profit_type}且金额比例差距过大，对敲可能性很低 (A:{pos_a_profit:.1f}, B:{pos_b_profit:.1f}, 比例:{ratio:.0f}:1)"
    
    return adjustment_factor, explanation


def demo_scale_aware_detection():
    """演示考虑规模的对敲检测"""
    
    print("=== 考虑仓位规模的对敲检测演示 ===\n")
    
    test_cases = [
        # 经典案例：小金额 vs 大金额
        (-1.0, -101.0, None, None, "小亏损vs大亏损(无交易量)"),
        (-1.0, -101.0, 100, 10000, "小亏损vs大亏损(有交易量)"),
        
        # 相近金额
        (-5.0, -6.0, None, None, "相近小亏损"),
        (-100.0, -120.0, None, None, "相近大亏损"),
        
        # 比例合理的情况
        (-2.0, -200.0, None, None, "1:100比例亏损"),
        (-5.0, -500.0, None, None, "1:100比例亏损(稍大)"),
        
        # 比例过大的情况
        (-1.0, -1000.0, None, None, "1:1000比例亏损"),
        
        # 双盈情况
        (1.0, 101.0, None, None, "小盈利vs大盈利"),
        (1.0, 101.0, 100, 10000, "小盈利vs大盈利(有交易量)"),
        
        # 一盈一亏（保持不变）
        (100.0, -95.0, None, None, "典型对敲"),
    ]
    
    for i, (pos_a, pos_b, vol_a, vol_b, desc) in enumerate(test_cases, 1):
        result = calculate_hedge_score_with_scale(pos_a, pos_b, vol_a, vol_b)
        
        vol_info = f", 量A={vol_a}, 量B={vol_b}" if vol_a and vol_b else ""
        print(f"案例{i}: {desc}")
        print(f"  仓位: A={pos_a:.1f}, B={pos_b:.1f}{vol_info}")
        print(f"  基础评分: {result['base_score']:.3f}")
        print(f"  调整因子: {result['adjustment_factor']:.1f}")
        print(f"  最终评分: {result['score']:.3f}")
        print(f"  风险等级: {result['risk_level']}")
        print(f"  说明: {result['explanation']}")
        print()


if __name__ == "__main__":
    demo_scale_aware_detection()
