#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合考虑的对敲检测算法
相对波动率作为补充参考，绝对金额仍然重要
"""

from dataclasses import dataclass
from typing import Tuple, Optional

@dataclass
class Position:
    """仓位信息"""
    real_profit: float  # 实际盈亏
    volume: float = 0   # 交易量/交易规模
    direction: str = '' # 方向

def calculate_comprehensive_hedge_score(pos_a: Position, pos_b: Position, 
                                      expected_scale: float = None) -> Tuple[float, dict]:
    """
    综合考虑的对敲检测算法
    
    核心思想：
    1. 保持原始公式作为基础
    2. 绝对金额控制：都很大的亏损/盈利不应该是对敲
    3. 相对波动率作为补充：在绝对金额合理的前提下参考
    
    Args:
        pos_a: 仓位A
        pos_b: 仓位B  
        expected_scale: 预期交易规模
    
    Returns:
        Tuple[float, dict]: (最终评分, 详细信息)
    """
    
    # === 基础计算（你的原始公式）===
    total_profit = pos_a.real_profit + pos_b.real_profit
    profit_sum = abs(pos_a.real_profit) + abs(pos_b.real_profit)
    
    if profit_sum == 0:
        return 0.0, {
            'base_score': 0.0,
            'final_score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'case_type': '无盈亏'
        }
    
    # 基础对敲评分
    base_score = 1.0 - abs(total_profit) / profit_sum
    
    # === 分情况处理 ===
    pos_a_profit = pos_a.real_profit
    pos_b_profit = pos_b.real_profit
    
    if pos_a_profit > 0 and pos_b_profit > 0:
        # 双盈情况
        case_type = "双盈"
        adjustment_factor, explanation = _calculate_double_profit_loss_adjustment(
            pos_a_profit, pos_b_profit, expected_scale, "盈利"
        )
        
    elif pos_a_profit < 0 and pos_b_profit < 0:
        # 双亏情况
        case_type = "双亏"
        adjustment_factor, explanation = _calculate_double_profit_loss_adjustment(
            pos_a_profit, pos_b_profit, expected_scale, "亏损"
        )
        
    else:
        # 一盈一亏情况：保持原逻辑
        case_type = "一盈一亏"
        
        if abs(total_profit) < min(abs(pos_a_profit), abs(pos_b_profit)) * 0.1:
            adjustment_factor = 1.2
            explanation = f"一盈一亏且几乎完全抵消，对敲特征明显"
        else:
            adjustment_factor = 1.0
            explanation = f"一盈一亏，符合对敲特征"
    
    # 最终评分
    final_score = min(base_score * adjustment_factor, 1.0)
    
    # 风险等级
    if final_score >= 0.8:
        risk_level = "高风险对敲"
    elif final_score >= 0.6:
        risk_level = "中等风险对敲"
    elif final_score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    # 详细信息
    details = {
        'base_score': base_score,
        'final_score': final_score,
        'adjustment_factor': adjustment_factor,
        'explanation': explanation,
        'case_type': case_type,
        'risk_level': risk_level,
        'total_profit': total_profit,
        'profit_sum': profit_sum
    }
    
    return final_score, details


def _calculate_double_profit_loss_adjustment(profit_a, profit_b, expected_scale, profit_type):
    """
    计算双盈/双亏情况的调整因子
    
    核心逻辑：
    1. 首先看绝对金额：如果都很大（如都亏50U），直接排除对敲可能
    2. 如果绝对金额合理，再看相对波动率作为补充
    3. 综合判断
    """
    
    abs_a = abs(profit_a)
    abs_b = abs(profit_b)
    max_abs = max(abs_a, abs_b)
    min_abs = min(abs_a, abs_b)
    
    # === 第一层：绝对金额控制 ===
    if max_abs >= 50:
        # 最大金额≥50U，波动太大，不太可能是对敲
        adjustment_factor = 0.1
        explanation = f"双{profit_type}且最大金额过大({max_abs:.1f}U≥50U)，波动太大，不太可能是对敲"
        return adjustment_factor, explanation
        
    elif max_abs >= 20:
        # 最大金额20-50U，波动较大，对敲可能性很低
        adjustment_factor = 0.2
        explanation = f"双{profit_type}且最大金额较大({max_abs:.1f}U≥20U)，波动较大，对敲可能性很低"
        return adjustment_factor, explanation
        
    elif max_abs >= 10:
        # 最大金额10-20U，波动适中，需要进一步判断
        base_factor = 0.4
        explanation_base = f"双{profit_type}且最大金额适中({max_abs:.1f}U)"
        
    else:
        # 最大金额<10U，波动较小，可以进一步分析
        base_factor = 0.7
        explanation_base = f"双{profit_type}且最大金额较小({max_abs:.1f}U)"
    
    # === 第二层：相对波动率补充（仅在绝对金额合理时使用）===
    if expected_scale and expected_scale > 0:
        # 有交易规模信息，计算相对波动
        rel_a = abs_a / expected_scale
        rel_b = abs_b / expected_scale
        max_rel = max(rel_a, rel_b)
        
        if max_rel <= 0.01:  # ≤1%
            rel_bonus = 0.3
            rel_explanation = f"且相对波动很小(≤1%)"
        elif max_rel <= 0.05:  # ≤5%
            rel_bonus = 0.2
            rel_explanation = f"且相对波动较小(≤5%)"
        elif max_rel <= 0.1:  # ≤10%
            rel_bonus = 0.1
            rel_explanation = f"且相对波动适中(≤10%)"
        else:
            rel_bonus = 0.0
            rel_explanation = f"且相对波动较大(>{max_rel:.1%})"
            
        explanation = f"{explanation_base}{rel_explanation}，相对规模{expected_scale:.1f}U"
        
    else:
        # 没有交易规模信息，基于金额比例判断
        if min_abs > 0:
            ratio = max_abs / min_abs
            if ratio <= 2:  # 金额相近
                rel_bonus = 0.2
                rel_explanation = f"且金额相近({ratio:.1f}:1)"
            elif ratio <= 10:  # 比例适中
                rel_bonus = 0.1
                rel_explanation = f"且比例适中({ratio:.1f}:1)"
            else:  # 比例较大
                rel_bonus = 0.0
                rel_explanation = f"且比例较大({ratio:.1f}:1)"
        else:
            rel_bonus = 0.0
            rel_explanation = ""
            
        explanation = f"{explanation_base}{rel_explanation}"
    
    # 最终调整因子
    final_factor = min(base_factor + rel_bonus, 1.0)
    
    # 根据最终因子确定对敲可能性描述
    if final_factor >= 0.8:
        possibility = "高对敲可能"
    elif final_factor >= 0.6:
        possibility = "中等对敲可能"
    elif final_factor >= 0.4:
        possibility = "低对敲可能"
    else:
        possibility = "对敲可能性很低"
    
    explanation += f"，{possibility}"
    
    return final_factor, explanation


def test_comprehensive_algorithm():
    """测试综合算法"""
    
    print("=== 综合考虑的对敲检测算法测试 ===\n")
    print("核心原则：")
    print("1. 绝对金额控制：都很大的波动不应该是对敲")
    print("2. 相对波动率补充：在绝对金额合理时作为参考")
    print("3. 保持原始公式作为基础\n")
    
    test_cases = [
        # 你的关键例子
        (Position(-0.03), Position(-1.0), 10.0, "-0.03 vs -1 (10U规模)"),
        (Position(-0.03), Position(-1.0), 100.0, "-0.03 vs -1 (100U规模)"),
        
        # 大金额情况（应该排除）
        (Position(-50.0), Position(-50.0), 100.0, "都亏50U (100U规模) - 应该排除"),
        (Position(-30.0), Position(-35.0), 100.0, "都亏30-35U (100U规模) - 应该排除"),
        
        # 中等金额情况
        (Position(-15.0), Position(-18.0), 100.0, "都亏15-18U (100U规模)"),
        (Position(-8.0), Position(-9.0), 100.0, "都亏8-9U (100U规模)"),
        
        # 小金额情况（应该关注）
        (Position(-2.0), Position(-3.0), 100.0, "都亏2-3U (100U规模)"),
        (Position(-0.5), Position(-1.5), 100.0, "都亏0.5-1.5U (100U规模)"),
        
        # 双盈情况
        (Position(50.0), Position(51.0), 100.0, "都盈50-51U (100U规模) - 应该排除"),
        (Position(2.0), Position(3.0), 100.0, "都盈2-3U (100U规模)"),
        
        # 一盈一亏（保持不变）
        (Position(10.0), Position(-9.5), 100.0, "10 vs -9.5 (100U规模)"),
    ]
    
    for i, (pos_a, pos_b, scale, description) in enumerate(test_cases, 1):
        score, details = calculate_comprehensive_hedge_score(pos_a, pos_b, scale)
        
        print(f"测试用例 {i}: {description}")
        print(f"仓位: A={pos_a.real_profit}, B={pos_b.real_profit}")
        print(f"基础评分: {details['base_score']:.3f}")
        print(f"调整因子: {details['adjustment_factor']:.1f}")
        print(f"最终评分: {details['final_score']:.3f}")
        print(f"风险等级: {details['risk_level']}")
        print(f"说明: {details['explanation']}")
        print("-" * 80)


if __name__ == "__main__":
    test_comprehensive_algorithm()
