#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的相对波动率对敲检测算法
只考虑相对波动率，但额外考虑金额大小
"""

from dataclasses import dataclass
from typing import Tuple, Optional

@dataclass
class Position:
    """仓位信息"""
    real_profit: float  # 实际盈亏
    volume: float = 0   # 交易量/交易规模
    direction: str = '' # 方向

def calculate_simple_relative_hedge_score(pos_a: Position, pos_b: Position, 
                                        expected_scale: float = None) -> Tuple[float, dict]:
    """
    简化的相对波动率对敲检测算法
    
    核心思路：
    1. 一盈一亏：使用原始公式
    2. 双盈/双亏：使用相对波动率 + 金额大小控制
    
    Args:
        pos_a: 仓位A
        pos_b: 仓位B  
        expected_scale: 预期交易规模
    
    Returns:
        Tuple[float, dict]: (最终评分, 详细信息)
    """
    
    # === 基础计算 ===
    total_profit = pos_a.real_profit + pos_b.real_profit
    profit_sum = abs(pos_a.real_profit) + abs(pos_b.real_profit)
    
    if profit_sum == 0:
        return 0.0, {
            'base_score': 0.0,
            'final_score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'case_type': '无盈亏'
        }
    
    # 基础评分（原始公式）
    base_score = 1.0 - abs(total_profit) / profit_sum
    
    pos_a_profit = pos_a.real_profit
    pos_b_profit = pos_b.real_profit
    
    # === 分情况处理 ===
    if (pos_a_profit > 0 and pos_b_profit < 0) or (pos_a_profit < 0 and pos_b_profit > 0):
        # 一盈一亏：直接使用原始公式
        case_type = "一盈一亏"
        final_score = base_score
        explanation = f"一盈一亏，使用原始公式，评分:{final_score:.3f}"
        
    else:
        # 双盈或双亏：使用相对波动率 + 金额大小控制
        if pos_a_profit > 0 and pos_b_profit > 0:
            case_type = "双盈"
        else:
            case_type = "双亏"
            
        final_score, explanation = _calculate_relative_volatility_score(
            pos_a_profit, pos_b_profit, expected_scale, case_type
        )
    
    # 风险等级
    if final_score >= 0.8:
        risk_level = "高风险对敲"
    elif final_score >= 0.6:
        risk_level = "中等风险对敲"
    elif final_score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    details = {
        'base_score': base_score,
        'final_score': final_score,
        'explanation': explanation,
        'case_type': case_type,
        'risk_level': risk_level,
        'total_profit': total_profit,
        'profit_sum': profit_sum
    }
    
    return final_score, details


def _calculate_relative_volatility_score(profit_a, profit_b, expected_scale, case_type):
    """
    基于相对波动率计算评分，额外考虑金额大小
    
    核心逻辑：
    1. 推测交易规模
    2. 计算相对波动率
    3. 金额大小控制：金额太大直接降分
    4. 相对波动率评分：波动越小分数越高
    """
    
    abs_a = abs(profit_a)
    abs_b = abs(profit_b)
    max_abs = max(abs_a, abs_b)
    
    # === 推测交易规模 ===
    if expected_scale and expected_scale > 0:
        scale = expected_scale
    else:
        # 简单推测：根据最大金额推测规模
        if max_abs <= 1:
            scale = 10.0
        elif max_abs <= 10:
            scale = 100.0
        elif max_abs <= 100:
            scale = 1000.0
        else:
            scale = max_abs * 10
    
    # === 金额大小控制 ===
    # 如果金额太大，直接给低分
    if max_abs >= 50:
        return 0.1, f"{case_type}，最大金额{max_abs:.1f}U过大(≥50U)，不太可能是对敲"
    elif max_abs >= 20:
        size_penalty = 0.3  # 大金额惩罚
        size_desc = f"最大金额{max_abs:.1f}U较大"
    elif max_abs >= 10:
        size_penalty = 0.6  # 中等金额
        size_desc = f"最大金额{max_abs:.1f}U适中"
    else:
        size_penalty = 1.0  # 小金额，无惩罚
        size_desc = f"最大金额{max_abs:.1f}U较小"
    
    # === 相对波动率评分 ===
    rel_a = abs_a / scale
    rel_b = abs_b / scale
    max_relative = max(rel_a, rel_b)
    avg_relative = (rel_a + rel_b) / 2
    
    # 根据相对波动率给分
    if max_relative <= 0.01:  # ≤1%
        volatility_score = 0.9
        volatility_desc = f"相对波动很小(≤1%)"
    elif max_relative <= 0.05:  # ≤5%
        volatility_score = 0.8
        volatility_desc = f"相对波动较小(≤5%)"
    elif max_relative <= 0.1:  # ≤10%
        volatility_score = 0.6
        volatility_desc = f"相对波动适中(≤10%)"
    elif max_relative <= 0.2:  # ≤20%
        volatility_score = 0.4
        volatility_desc = f"相对波动较大(≤20%)"
    else:  # >20%
        volatility_score = 0.2
        volatility_desc = f"相对波动很大(>{max_relative:.1%})"
    
    # === 最终评分 ===
    # 金额大小控制 × 相对波动率评分
    final_score = size_penalty * volatility_score
    
    # 详细说明
    explanation = (f"{case_type}，{size_desc}，{volatility_desc} "
                  f"(A:{rel_a:.1%}, B:{rel_b:.1%}, 规模{scale:.0f}U)，"
                  f"评分:{final_score:.3f}")
    
    return final_score, explanation


def test_simple_relative_algorithm():
    """测试简化的相对波动率算法"""
    
    print("=== 简化的相对波动率对敲检测算法测试 ===\n")
    print("核心逻辑：")
    print("1. 一盈一亏：使用原始公式")
    print("2. 双盈/双亏：相对波动率 + 金额大小控制")
    print("3. 金额控制：≥50U直接低分，20-50U惩罚，<20U正常")
    print("4. 波动控制：≤1%高分，1-5%较高，5-10%适中，10-20%较低，>20%低分\n")
    
    test_cases = [
        # 一盈一亏情况（原始公式）
        (Position(10.0), Position(-9.5), 100.0, "一盈一亏-几乎抵消"),
        (Position(1.0), Position(-0.9), 100.0, "一盈一亏-小金额"),
        
        # 你的关键例子
        (Position(-0.03), Position(-1.0), 10.0, "双亏-你的例子(10U规模)"),
        (Position(-0.03), Position(-1.0), 100.0, "双亏-你的例子(100U规模)"),
        
        # 不同金额大小的双亏
        (Position(-2.0), Position(-3.0), 100.0, "双亏-小金额(<10U)"),
        (Position(-15.0), Position(-18.0), 100.0, "双亏-中等金额(10-20U)"),
        (Position(-30.0), Position(-35.0), 100.0, "双亏-较大金额(20-50U)"),
        (Position(-60.0), Position(-65.0), 100.0, "双亏-大金额(≥50U)"),
        
        # 不同相对波动的双亏
        (Position(-0.5), Position(-1.0), 100.0, "双亏-很小波动(0.5-1%)"),
        (Position(-2.5), Position(-5.0), 100.0, "双亏-小波动(2.5-5%)"),
        (Position(-7.5), Position(-15.0), 100.0, "双亏-中等波动(7.5-15%)"),
        (Position(-15.0), Position(-30.0), 100.0, "双亏-大波动(15-30%)"),
        
        # 双盈情况
        (Position(2.0), Position(3.0), 100.0, "双盈-小金额小波动"),
        (Position(30.0), Position(35.0), 100.0, "双盈-较大金额"),
        (Position(60.0), Position(65.0), 100.0, "双盈-大金额"),
        
        # 特殊情况
        (Position(-0.1), Position(-0.2), 100.0, "双亏-极小金额"),
        (Position(-100.0), Position(-101.0), 1000.0, "双亏-大金额但相对波动小"),
    ]
    
    for i, (pos_a, pos_b, scale, description) in enumerate(test_cases, 1):
        score, details = calculate_simple_relative_hedge_score(pos_a, pos_b, scale)
        
        print(f"测试用例 {i}: {description}")
        print(f"仓位: A={pos_a.real_profit}, B={pos_b.real_profit} (规模{scale}U)")
        print(f"情况类型: {details['case_type']}")
        print(f"基础评分: {details['base_score']:.3f}")
        print(f"最终评分: {details['final_score']:.3f}")
        print(f"风险等级: {details['risk_level']}")
        print(f"说明: {details['explanation']}")
        print("-" * 80)


if __name__ == "__main__":
    test_simple_relative_algorithm()
