#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的比例控制对敲检测算法
基于原始公式，结合比例差距控制
"""

from dataclasses import dataclass
from typing import Tuple, Optional

@dataclass
class Position:
    """仓位信息"""
    real_profit: float  # 实际盈亏
    volume: float = 0   # 交易量
    direction: str = '' # 方向

def calculate_profit_hedge_score_with_ratio(pos_a: Position, pos_b: Position) -> Tuple[float, dict]:
    """
    完整的对敲检测算法
    基于原始公式 + 比例差距控制
    
    Args:
        pos_a: 仓位A
        pos_b: 仓位B
    
    Returns:
        Tuple[float, dict]: (最终评分, 详细信息)
    """
    
    # === 你的原始公式（核心不变）===
    total_profit = pos_a.real_profit + pos_b.real_profit
    profit_sum = abs(pos_a.real_profit) + abs(pos_b.real_profit)
    
    if profit_sum == 0:
        return 0.0, {
            'base_score': 0.0,
            'final_score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'case_type': '无盈亏',
            'risk_level': '无法判断'
        }
    
    # 基础对敲评分：总盈亏越接近0，评分越高
    profit_hedge_score = 1.0 - abs(total_profit) / profit_sum
    
    # === 比例差距控制逻辑 ===
    pos_a_profit = pos_a.real_profit
    pos_b_profit = pos_b.real_profit
    
    # 计算金额比例
    abs_a = abs(pos_a_profit)
    abs_b = abs(pos_b_profit)
    
    if min(abs_a, abs_b) > 0:
        ratio = max(abs_a, abs_b) / min(abs_a, abs_b)
    else:
        ratio = 1.0
    
    # 根据盈亏组合和比例进行调整
    if pos_a_profit > 0 and pos_b_profit > 0:
        # 双盈情况
        case_type = "双盈"
        adjustment_factor, explanation = _calculate_ratio_adjustment(
            abs_a, abs_b, ratio, "盈利", profit_hedge_score
        )
        
    elif pos_a_profit < 0 and pos_b_profit < 0:
        # 双亏情况
        case_type = "双亏"
        adjustment_factor, explanation = _calculate_ratio_adjustment(
            abs_a, abs_b, ratio, "亏损", profit_hedge_score
        )
        
    else:
        # 一盈一亏情况：保持原逻辑，但也考虑比例
        case_type = "一盈一亏"
        
        if abs(total_profit) < min(abs_a, abs_b) * 0.1:
            # 几乎完全抵消
            if ratio <= 5:  # 比例合理
                adjustment_factor = 1.2
                explanation = f"一盈一亏且几乎完全抵消，比例合理({ratio:.1f}:1)，对敲特征明显"
            else:  # 比例较大但仍抵消
                adjustment_factor = 1.1
                explanation = f"一盈一亏且几乎完全抵消，但比例较大({ratio:.1f}:1)，仍有对敲特征"
        else:
            # 未完全抵消
            if ratio <= 3:  # 比例合理
                adjustment_factor = 1.0
                explanation = f"一盈一亏，比例合理({ratio:.1f}:1)，符合对敲特征"
            else:  # 比例较大
                adjustment_factor = 0.8
                explanation = f"一盈一亏，但比例较大({ratio:.1f}:1)，对敲特征减弱"
    
    # 最终评分
    final_score = min(profit_hedge_score * adjustment_factor, 1.0)
    
    # 风险等级判定
    if final_score >= 0.8:
        risk_level = "高风险对敲"
    elif final_score >= 0.6:
        risk_level = "中等风险对敲"
    elif final_score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    # 返回详细信息
    details = {
        'base_score': profit_hedge_score,
        'final_score': final_score,
        'adjustment_factor': adjustment_factor,
        'ratio': ratio,
        'explanation': explanation,
        'case_type': case_type,
        'risk_level': risk_level,
        'total_profit': total_profit,
        'profit_sum': profit_sum,
        'abs_a': abs_a,
        'abs_b': abs_b
    }
    
    return final_score, details


def _calculate_ratio_adjustment(abs_a, abs_b, ratio, profit_type, base_score):
    """
    基于比例计算调整因子
    
    核心逻辑：
    1. 比例越接近1:1，对敲可能性越高
    2. 小金额接近0 + 合理比例，也有对敲可能
    3. 比例过大，对敲可能性降低
    """
    
    min_amount = min(abs_a, abs_b)
    max_amount = max(abs_a, abs_b)
    
    if ratio <= 2:
        # 比例很接近（≤2:1）
        if max_amount <= 10:
            adjustment_factor = 0.9
            explanation = f"双{profit_type}且金额相近且都较小({ratio:.1f}:1)，有对敲可能"
        elif max_amount <= 100:
            adjustment_factor = 0.7
            explanation = f"双{profit_type}且金额相近({ratio:.1f}:1)，中等对敲可能"
        else:
            adjustment_factor = 0.5
            explanation = f"双{profit_type}且金额相近但较大({ratio:.1f}:1)，低对敲可能"
            
    elif 2 < ratio <= 10:
        # 比例适中（2-10:1）
        if min_amount <= 5:  # 小金额很小
            adjustment_factor = 0.8
            explanation = f"双{profit_type}且小金额接近0，比例适中({ratio:.1f}:1)，有对敲可能"
        elif min_amount <= 20:
            adjustment_factor = 0.6
            explanation = f"双{profit_type}且比例适中({ratio:.1f}:1)，中等对敲可能"
        else:
            adjustment_factor = 0.4
            explanation = f"双{profit_type}且比例适中但金额较大({ratio:.1f}:1)，低对敲可能"
            
    elif 10 < ratio <= 200:
        # 比例较大但合理（10-200:1，如你的例子1U vs 101U）
        if min_amount <= 5:  # 小金额很小，可能是对敲
            # 进一步检查基础评分，如果基础评分高说明总和接近0
            if base_score > 0.8:  # 总盈亏很接近0
                adjustment_factor = 0.7
                explanation = f"双{profit_type}且小金额接近0，总和接近0，比例合理({ratio:.0f}:1)，有对敲可能"
            else:
                adjustment_factor = 0.5
                explanation = f"双{profit_type}且小金额接近0，比例较大({ratio:.0f}:1)，中等对敲可能"
        else:
            adjustment_factor = 0.3
            explanation = f"双{profit_type}且比例较大({ratio:.0f}:1)，对敲可能性较低"
            
    else:
        # 比例过大（>200:1）
        adjustment_factor = 0.2
        explanation = f"双{profit_type}且比例过大({ratio:.0f}:1)，对敲可能性很低"
    
    return adjustment_factor, explanation


def test_complete_algorithm():
    """测试完整算法"""
    
    print("=== 完整的比例控制对敲检测算法测试 ===\n")
    print("基于原始公式：profit_hedge_score = 1.0 - abs(total_profit) / profit_sum")
    print("结合比例差距控制\n")
    
    test_cases = [
        # 一盈一亏情况
        (Position(100.0), Position(-95.0), "典型对敲(接近抵消)"),
        (Position(100.0), Position(-100.0), "完全对冲"),
        (Position(100.0), Position(-10.0), "一盈一亏(比例大)"),
        (Position(1000.0), Position(-100.0), "一盈一亏(比例很大)"),
        
        # 双亏情况 - 你的关键例子
        (Position(-1.0), Position(-101.0), "小亏vs大亏(1:101)"),
        (Position(-2.0), Position(-200.0), "小亏vs大亏(1:100)"),
        (Position(-5.0), Position(-6.0), "相近小亏损"),
        (Position(-100.0), Position(-120.0), "相近大亏损"),
        (Position(-1.0), Position(-1000.0), "极大比例亏损"),
        
        # 双盈情况
        (Position(1.0), Position(101.0), "小盈vs大盈(1:101)"),
        (Position(5.0), Position(6.0), "相近小盈利"),
        (Position(100.0), Position(120.0), "相近大盈利"),
        
        # 特殊情况：总和接近0的双亏/双盈
        (Position(-50.0), Position(-51.0), "双亏总和接近0"),
        (Position(50.0), Position(51.0), "双盈总和接近0"),
    ]
    
    for i, (pos_a, pos_b, description) in enumerate(test_cases, 1):
        score, details = calculate_profit_hedge_score_with_ratio(pos_a, pos_b)
        
        print(f"测试用例 {i}: {description}")
        print(f"仓位: A={pos_a.real_profit:.1f}, B={pos_b.real_profit:.1f}")
        print(f"总盈亏: {details['total_profit']:.1f}, 盈亏总和: {details['profit_sum']:.1f}")
        print(f"金额比例: {details['ratio']:.1f}:1")
        print(f"基础评分: {details['base_score']:.3f} (原始公式)")
        print(f"调整因子: {details['adjustment_factor']:.1f}")
        print(f"最终评分: {details['final_score']:.3f}")
        print(f"风险等级: {details['risk_level']}")
        print(f"说明: {details['explanation']}")
        print("-" * 80)


def test_specific_case():
    """测试特定案例：-0.03 vs -1"""
    print("=== 特定案例测试：-0.03 vs -1 ===\n")

    pos_a = Position(-0.03)
    pos_b = Position(-1.0)

    score, details = calculate_profit_hedge_score_with_ratio(pos_a, pos_b)

    print(f"仓位: A={pos_a.real_profit}, B={pos_b.real_profit}")
    print(f"总盈亏: {details['total_profit']}")
    print(f"盈亏总和: {details['profit_sum']}")
    print(f"金额比例: {details['ratio']:.1f}:1")
    print(f"基础评分: {details['base_score']:.3f} (原始公式)")
    print(f"调整因子: {details['adjustment_factor']:.1f}")
    print(f"最终评分: {details['final_score']:.3f}")
    print(f"风险等级: {details['risk_level']}")
    print(f"说明: {details['explanation']}")

    # 分析差距
    abs_a = abs(pos_a.real_profit)
    abs_b = abs(pos_b.real_profit)
    ratio = max(abs_a, abs_b) / min(abs_a, abs_b)

    print(f"\n=== 详细分析 ===")
    print(f"A的绝对值: {abs_a}")
    print(f"B的绝对值: {abs_b}")
    print(f"比例: {ratio:.1f}:1")
    print(f"小金额: {min(abs_a, abs_b)}")
    print(f"大金额: {max(abs_a, abs_b)}")

    if ratio <= 2:
        print("比例判断: 很接近 (≤2:1)")
    elif 2 < ratio <= 10:
        print("比例判断: 适中 (2-10:1)")
    elif 10 < ratio <= 200:
        print("比例判断: 较大但合理 (10-200:1)")
    else:
        print("比例判断: 过大 (>200:1)")


if __name__ == "__main__":
    test_specific_case()
    print("\n" + "="*80 + "\n")
    test_complete_algorithm()
