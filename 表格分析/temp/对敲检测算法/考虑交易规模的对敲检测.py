#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考虑交易规模的对敲检测算法
重新思考：-0.03 vs -1 在10U交易中应该是高对敲可能性
"""

from dataclasses import dataclass
from typing import Tuple, Optional

@dataclass
class Position:
    """仓位信息"""
    real_profit: float  # 实际盈亏
    volume: float = 0   # 交易量/交易规模
    direction: str = '' # 方向

def calculate_hedge_score_with_scale_context(pos_a: Position, pos_b: Position, 
                                           expected_scale: float = None) -> Tuple[float, dict]:
    """
    考虑交易规模背景的对敲检测算法
    
    核心思想：
    1. 如果知道交易规模，计算相对波动率
    2. 如果不知道交易规模，通过盈亏金额推测规模
    3. 重点关注：两个仓位是否都相对于规模很小
    
    Args:
        pos_a: 仓位A
        pos_b: 仓位B  
        expected_scale: 预期交易规模（如10U、100U等）
    
    Returns:
        Tuple[float, dict]: (最终评分, 详细信息)
    """
    
    # === 基础计算 ===
    total_profit = pos_a.real_profit + pos_b.real_profit
    profit_sum = abs(pos_a.real_profit) + abs(pos_b.real_profit)
    
    if profit_sum == 0:
        return 0.0, {
            'base_score': 0.0,
            'final_score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'case_type': '无盈亏'
        }
    
    # 基础对敲评分
    base_score = 1.0 - abs(total_profit) / profit_sum
    
    # === 推测或使用交易规模 ===
    if expected_scale:
        # 使用提供的交易规模
        scale = expected_scale
    elif pos_a.volume > 0 and pos_b.volume > 0:
        # 使用交易量作为规模参考
        scale = (pos_a.volume + pos_b.volume) / 2
    else:
        # 根据盈亏金额推测规模
        max_profit = max(abs(pos_a.real_profit), abs(pos_b.real_profit))
        if max_profit <= 0.1:
            scale = 1.0  # 推测小规模交易
        elif max_profit <= 1:
            scale = 10.0  # 推测中小规模交易
        elif max_profit <= 10:
            scale = 100.0  # 推测中等规模交易
        else:
            scale = max_profit * 10  # 推测大规模交易
    
    # === 计算相对波动率 ===
    relative_profit_a = abs(pos_a.real_profit) / scale
    relative_profit_b = abs(pos_b.real_profit) / scale
    max_relative = max(relative_profit_a, relative_profit_b)
    
    # === 根据盈亏组合和相对波动调整 ===
    pos_a_profit = pos_a.real_profit
    pos_b_profit = pos_b.real_profit
    
    if pos_a_profit > 0 and pos_b_profit > 0:
        # 双盈情况
        case_type = "双盈"
        adjustment_factor, explanation = _calculate_relative_adjustment(
            relative_profit_a, relative_profit_b, max_relative, scale, "盈利"
        )
        
    elif pos_a_profit < 0 and pos_b_profit < 0:
        # 双亏情况
        case_type = "双亏"
        adjustment_factor, explanation = _calculate_relative_adjustment(
            relative_profit_a, relative_profit_b, max_relative, scale, "亏损"
        )
        
    else:
        # 一盈一亏情况
        case_type = "一盈一亏"
        
        if abs(total_profit) < min(abs(pos_a_profit), abs(pos_b_profit)) * 0.1:
            adjustment_factor = 1.2
            explanation = f"一盈一亏且几乎完全抵消，对敲特征明显"
        else:
            adjustment_factor = 1.0
            explanation = f"一盈一亏，符合对敲特征"
    
    # 最终评分
    final_score = min(base_score * adjustment_factor, 1.0)
    
    # 风险等级
    if final_score >= 0.8:
        risk_level = "高风险对敲"
    elif final_score >= 0.6:
        risk_level = "中等风险对敲"
    elif final_score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    # 详细信息
    details = {
        'base_score': base_score,
        'final_score': final_score,
        'adjustment_factor': adjustment_factor,
        'explanation': explanation,
        'case_type': case_type,
        'risk_level': risk_level,
        'scale': scale,
        'relative_profit_a': relative_profit_a,
        'relative_profit_b': relative_profit_b,
        'max_relative': max_relative,
        'total_profit': total_profit,
        'profit_sum': profit_sum
    }
    
    return final_score, details


def _calculate_relative_adjustment(rel_a, rel_b, max_rel, scale, profit_type):
    """
    基于相对波动率计算调整因子
    
    核心逻辑：
    1. 如果两个相对波动都很小（如都<1%），高对敲可能
    2. 如果一个很小一个稍大但仍合理，中等对敲可能
    3. 如果波动都较大，低对敲可能
    """
    
    if max_rel <= 0.01:  # 最大相对波动≤1%
        adjustment_factor = 0.9
        explanation = f"双{profit_type}且相对波动都很小(≤1%)，高对敲可能 (A:{rel_a:.2%}, B:{rel_b:.2%}, 规模:{scale:.1f})"
        
    elif max_rel <= 0.05:  # 最大相对波动≤5%
        adjustment_factor = 0.8
        explanation = f"双{profit_type}且相对波动较小(≤5%)，高对敲可能 (A:{rel_a:.2%}, B:{rel_b:.2%}, 规模:{scale:.1f})"
        
    elif max_rel <= 0.1:  # 最大相对波动≤10%
        adjustment_factor = 0.6
        explanation = f"双{profit_type}且相对波动适中(≤10%)，中等对敲可能 (A:{rel_a:.2%}, B:{rel_b:.2%}, 规模:{scale:.1f})"
        
    elif max_rel <= 0.2:  # 最大相对波动≤20%
        adjustment_factor = 0.4
        explanation = f"双{profit_type}且相对波动较大(≤20%)，低对敲可能 (A:{rel_a:.2%}, B:{rel_b:.2%}, 规模:{scale:.1f})"
        
    else:  # 相对波动>20%
        adjustment_factor = 0.2
        explanation = f"双{profit_type}且相对波动很大(>20%)，对敲可能性很低 (A:{rel_a:.2%}, B:{rel_b:.2%}, 规模:{scale:.1f})"
    
    return adjustment_factor, explanation


def test_scale_context_algorithm():
    """测试考虑交易规模的算法"""
    
    print("=== 考虑交易规模背景的对敲检测测试 ===\n")
    
    # 重点测试：-0.03 vs -1 在不同交易规模下的表现
    test_cases = [
        # 你的关键例子：在不同规模下
        (Position(-0.03), Position(-1.0), 10.0, "-0.03 vs -1 (10U规模)"),
        (Position(-0.03), Position(-1.0), 100.0, "-0.03 vs -1 (100U规模)"),
        (Position(-0.03), Position(-1.0), None, "-0.03 vs -1 (自动推测规模)"),
        
        # 其他对比案例
        (Position(-0.3), Position(-10.0), 100.0, "-0.3 vs -10 (100U规模)"),
        (Position(-3.0), Position(-100.0), 1000.0, "-3 vs -100 (1000U规模)"),
        
        # 相对波动很小的案例
        (Position(-0.1), Position(-0.2), 100.0, "-0.1 vs -0.2 (100U规模)"),
        (Position(-1.0), Position(-2.0), 1000.0, "-1 vs -2 (1000U规模)"),
        
        # 一盈一亏案例
        (Position(0.5), Position(-0.6), 10.0, "0.5 vs -0.6 (10U规模)"),
        (Position(10.0), Position(-9.5), 100.0, "10 vs -9.5 (100U规模)"),
    ]
    
    for i, (pos_a, pos_b, scale, description) in enumerate(test_cases, 1):
        score, details = calculate_hedge_score_with_scale_context(pos_a, pos_b, scale)
        
        scale_info = f"规模{scale:.1f}U" if scale else "自动推测"
        print(f"测试用例 {i}: {description}")
        print(f"仓位: A={pos_a.real_profit}, B={pos_b.real_profit} ({scale_info})")
        print(f"推测/使用规模: {details['scale']:.1f}")
        print(f"相对波动: A={details['relative_profit_a']:.2%}, B={details['relative_profit_b']:.2%}")
        print(f"最大相对波动: {details['max_relative']:.2%}")
        print(f"基础评分: {details['base_score']:.3f}")
        print(f"调整因子: {details['adjustment_factor']:.1f}")
        print(f"最终评分: {details['final_score']:.3f}")
        print(f"风险等级: {details['risk_level']}")
        print(f"说明: {details['explanation']}")
        print("-" * 80)


if __name__ == "__main__":
    test_scale_context_algorithm()
