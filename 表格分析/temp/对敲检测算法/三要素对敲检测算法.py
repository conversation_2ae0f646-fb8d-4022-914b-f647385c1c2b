#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三要素对敲检测算法
只用：相对波动率 + 越接近0 + 原始公式
"""

from dataclasses import dataclass
from typing import Tuple, Optional
import math

@dataclass
class Position:
    """仓位信息"""
    real_profit: float  # 实际盈亏
    volume: float = 0   # 交易量/交易规模
    direction: str = '' # 方向

def calculate_three_factor_hedge_score(pos_a: Position, pos_b: Position, 
                                     expected_scale: float = None) -> Tuple[float, dict]:
    """
    三要素对敲检测算法
    
    要素1: 原始公式 - profit_hedge_score = 1.0 - abs(total_profit) / profit_sum
    要素2: 越接近0 - 个体盈亏越接近0越好
    要素3: 相对波动率 - 相对于交易规模的波动越小越好
    
    Args:
        pos_a: 仓位A
        pos_b: 仓位B  
        expected_scale: 预期交易规模
    
    Returns:
        Tuple[float, dict]: (最终评分, 详细信息)
    """
    
    # === 要素1: 原始公式（基础评分）===
    total_profit = pos_a.real_profit + pos_b.real_profit
    profit_sum = abs(pos_a.real_profit) + abs(pos_b.real_profit)
    
    if profit_sum == 0:
        return 0.0, {
            'base_score': 0.0,
            'final_score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'factors': {}
        }
    
    # 基础评分：总盈亏越接近0，评分越高
    base_score = 1.0 - abs(total_profit) / profit_sum
    
    # === 要素2: 越接近0评分 ===
    # 计算每个仓位距离0的程度
    abs_a = abs(pos_a.real_profit)
    abs_b = abs(pos_b.real_profit)
    
    # 使用指数衰减函数：越接近0分数越高
    # f(x) = e^(-x/k)，其中k是衰减参数
    k = 5.0  # 衰减参数，可调整
    closeness_score_a = math.exp(-abs_a / k)
    closeness_score_b = math.exp(-abs_b / k)
    closeness_score = (closeness_score_a + closeness_score_b) / 2
    
    # === 要素3: 相对波动率评分 ===
    if expected_scale and expected_scale > 0:
        # 有交易规模信息
        scale = expected_scale
    elif pos_a.volume > 0 and pos_b.volume > 0:
        # 使用交易量作为规模参考
        scale = (pos_a.volume + pos_b.volume) / 2
    else:
        # 根据盈亏金额推测规模
        max_profit = max(abs_a, abs_b)
        if max_profit <= 1:
            scale = 10.0
        elif max_profit <= 10:
            scale = 100.0
        else:
            scale = max_profit * 10
    
    # 计算相对波动率
    relative_volatility_a = abs_a / scale
    relative_volatility_b = abs_b / scale
    avg_relative_volatility = (relative_volatility_a + relative_volatility_b) / 2
    
    # 相对波动率评分：波动越小分数越高
    # 使用指数衰减：f(x) = e^(-x/k)
    volatility_k = 0.05  # 5%作为衰减参数
    volatility_score = math.exp(-avg_relative_volatility / volatility_k)
    
    # === 综合评分 ===
    # 三个要素的权重
    w1 = 0.4  # 原始公式权重
    w2 = 0.3  # 接近0权重  
    w3 = 0.3  # 相对波动率权重
    
    # 加权平均
    final_score = w1 * base_score + w2 * closeness_score + w3 * volatility_score
    
    # 确保评分在0-1范围内
    final_score = max(0.0, min(1.0, final_score))
    
    # 风险等级
    if final_score >= 0.8:
        risk_level = "高风险对敲"
    elif final_score >= 0.6:
        risk_level = "中等风险对敲"
    elif final_score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    # 详细信息
    factors = {
        'base_score': base_score,
        'closeness_score': closeness_score,
        'volatility_score': volatility_score,
        'closeness_a': closeness_score_a,
        'closeness_b': closeness_score_b,
        'relative_volatility_a': relative_volatility_a,
        'relative_volatility_b': relative_volatility_b,
        'avg_relative_volatility': avg_relative_volatility,
        'scale': scale
    }
    
    explanation = (f"原始公式:{base_score:.3f} + 接近0:{closeness_score:.3f} + "
                  f"相对波动:{volatility_score:.3f} = {final_score:.3f}")
    
    details = {
        'base_score': base_score,
        'final_score': final_score,
        'explanation': explanation,
        'risk_level': risk_level,
        'factors': factors,
        'total_profit': total_profit,
        'profit_sum': profit_sum
    }
    
    return final_score, details


def test_three_factor_algorithm():
    """测试三要素算法"""
    
    print("=== 三要素对敲检测算法测试 ===\n")
    print("要素1: 原始公式 (40%权重)")
    print("要素2: 越接近0 (30%权重)")  
    print("要素3: 相对波动率 (30%权重)\n")
    
    test_cases = [
        # 经典案例
        (Position(-0.03), Position(-1.0), 10.0, "-0.03 vs -1 (10U规模)"),
        (Position(-0.03), Position(-1.0), 100.0, "-0.03 vs -1 (100U规模)"),
        
        # 不同接近0程度
        (Position(-0.1), Position(-0.2), 100.0, "都很接近0"),
        (Position(-1.0), Position(-2.0), 100.0, "较接近0"),
        (Position(-10.0), Position(-20.0), 100.0, "远离0"),
        (Position(-50.0), Position(-50.0), 100.0, "很远离0"),
        
        # 不同相对波动率
        (Position(-0.5), Position(-1.0), 100.0, "低相对波动(0.5-1%)"),
        (Position(-5.0), Position(-10.0), 100.0, "中相对波动(5-10%)"),
        (Position(-25.0), Position(-30.0), 100.0, "高相对波动(25-30%)"),
        
        # 一盈一亏情况
        (Position(1.0), Position(-0.9), 100.0, "一盈一亏接近抵消"),
        (Position(10.0), Position(-9.0), 100.0, "一盈一亏较大金额"),
        (Position(0.1), Position(-0.1), 100.0, "一盈一亏很小金额"),
        
        # 双盈情况
        (Position(0.5), Position(1.0), 100.0, "双盈小金额"),
        (Position(5.0), Position(10.0), 100.0, "双盈中等金额"),
        
        # 极端情况
        (Position(0.0), Position(-0.01), 100.0, "一个为0"),
        (Position(-100.0), Position(-101.0), 1000.0, "大金额但相对波动小"),
    ]
    
    for i, (pos_a, pos_b, scale, description) in enumerate(test_cases, 1):
        score, details = calculate_three_factor_hedge_score(pos_a, pos_b, scale)
        factors = details['factors']
        
        print(f"测试用例 {i}: {description}")
        print(f"仓位: A={pos_a.real_profit}, B={pos_b.real_profit} (规模{scale}U)")
        print(f"要素1-原始公式: {factors['base_score']:.3f}")
        print(f"要素2-接近0: {factors['closeness_score']:.3f} (A:{factors['closeness_a']:.3f}, B:{factors['closeness_b']:.3f})")
        print(f"要素3-相对波动: {factors['volatility_score']:.3f} (A:{factors['relative_volatility_a']:.1%}, B:{factors['relative_volatility_b']:.1%})")
        print(f"最终评分: {details['final_score']:.3f}")
        print(f"风险等级: {details['risk_level']}")
        print(f"计算过程: {details['explanation']}")
        print("-" * 80)


if __name__ == "__main__":
    test_three_factor_algorithm()
