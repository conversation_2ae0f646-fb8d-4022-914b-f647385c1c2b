#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整对敲检测算法 - 基于开单金额的核心版本
"""

def calculate_hedge_score(profit_a, profit_b, order_amount_a, order_amount_b):
    """
    对敲检测核心算法
    
    Args:
        profit_a (float): 仓位A的实际盈亏
        profit_b (float): 仓位B的实际盈亏  
        order_amount_a (float): 仓位A的开单金额
        order_amount_b (float): 仓位B的开单金额
    
    Returns:
        tuple: (评分, 风险等级)
            评分: 0.0-1.0，越高越可能是对敲
            风险等级: "高风险对敲"/"中等风险对敲"/"低风险对敲"/"正常交易"
    """
    
    # ==================== 基础检查 ====================
    profit_sum = abs(profit_a) + abs(profit_b)
    order_sum = order_amount_a + order_amount_b
    
    # 无盈亏或无开单金额，视为正常交易
    if profit_sum == 0 or order_sum == 0:
        return 0.0, "正常交易"
    
    # ==================== 判断同边/异边 ====================
    # 同边：都盈利或都亏损；异边：一盈一亏
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    
    if not is_same_side:
        # ==================== 异边情况：经典抵消度公式 ====================
        total_profit = profit_a + profit_b
        score = 1.0 - abs(total_profit) / profit_sum
        
        # 异边风险等级判断
        if score >= 0.8:
            risk_level = "高风险对敲"
        elif score >= 0.6:
            risk_level = "中等风险对敲"
        elif score >= 0.4:
            risk_level = "低风险对敲"
        else:
            risk_level = "正常交易"
            
    else:
        # ==================== 同边情况：五因子综合算法 ====================
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        max_abs = max(abs_a, abs_b)
        
        # 使用真实开单金额作为交易规模
        scale = order_sum
        
        # ---------- 因子1：接近0程度评分 ----------
        # 基于最小盈亏相对于开单金额的比例
        min_relative_to_order = min_abs / scale
        if min_relative_to_order <= 0.001:     # ≤0.1%，几乎无盈亏
            closeness_score = 0.95
        elif min_relative_to_order <= 0.005:   # ≤0.5%，很接近0
            closeness_score = 0.9
        elif min_relative_to_order <= 0.01:    # ≤1%，接近0
            closeness_score = 0.8
        elif min_relative_to_order <= 0.02:    # ≤2%，比较接近0
            closeness_score = 0.6
        elif min_relative_to_order <= 0.05:    # ≤5%，稍微接近0
            closeness_score = 0.4
        elif min_relative_to_order <= 0.1:     # ≤10%，不太接近0
            closeness_score = 0.2
        else:                                   # >10%，远离0
            closeness_score = 0.1
        
        # ---------- 因子2：相对波动率评分 ----------
        # 基于最大盈亏相对于开单金额的比例
        max_relative = max(abs_a / scale, abs_b / scale)
        if max_relative <= 0.01:       # ≤1%，波动很小
            volatility_score = 0.9
        elif max_relative <= 0.03:     # ≤3%，波动较小
            volatility_score = 0.8
        elif max_relative <= 0.05:     # ≤5%，波动中等
            volatility_score = 0.6
        elif max_relative <= 0.1:      # ≤10%，波动较大
            volatility_score = 0.4
        else:                          # >10%，波动很大
            volatility_score = 0.2
        
        # ---------- 因子3：差异度评分 ----------
        # 两个盈亏金额的相对差异，差异越大越可能是对敲
        diff_ratio = abs(abs_a - abs_b) / max_abs
        if diff_ratio >= 0.8:          # 差异很大（如0.1 vs 5）
            diff_score = 0.9
        elif diff_ratio >= 0.5:        # 差异较大（如2 vs 5）
            diff_score = 0.7
        elif diff_ratio >= 0.2:        # 差异中等（如4 vs 5）
            diff_score = 0.5
        else:                          # 差异很小（如4.5 vs 5）
            diff_score = 0.3
        
        # ---------- 因子4：盈亏率对比评分 ----------
        # 对敲的核心特征：一方盈亏率很小，另一方盈亏率正常
        profit_rate_a = abs_a / order_amount_a if order_amount_a > 0 else 0
        profit_rate_b = abs_b / order_amount_b if order_amount_b > 0 else 0
        
        min_profit_rate = min(profit_rate_a, profit_rate_b)
        max_profit_rate = max(profit_rate_a, profit_rate_b)
        
        # 理想的对敲模式：一方盈亏率很低，另一方盈亏率正常
        if min_profit_rate <= 0.005 and max_profit_rate >= 0.02:    # 一方≤0.5%，另一方≥2%
            profit_rate_score = 0.95
        elif min_profit_rate <= 0.01 and max_profit_rate >= 0.015:  # 一方≤1%，另一方≥1.5%
            profit_rate_score = 0.9
        elif min_profit_rate <= 0.02 and max_profit_rate >= 0.03:   # 一方≤2%，另一方≥3%
            profit_rate_score = 0.8
        elif min_profit_rate <= 0.03 and max_profit_rate >= 0.05:   # 一方≤3%，另一方≥5%
            profit_rate_score = 0.6
        else:
            # 两方盈亏率都很高或都很低，不太像对敲
            if max_profit_rate <= 0.01:    # 都很低，可能是正常的小波动
                profit_rate_score = 0.3
            else:                          # 都很高，可能是正常交易
                profit_rate_score = 0.2
        
        # ---------- 动态权重计算最终评分 ----------
        # 根据盈利金额差异调整四因子权重
        if min_profit_rate <= 0.005:
            # 确实有一方盈亏率很低，强调盈亏率对比和接近0程度
            score = (0.45 * profit_rate_score +
                    0.35 * closeness_score +
                    0.15 * volatility_score +
                    0.05 * diff_score)
        elif min_profit_rate <= 0.02:
            # 一方盈亏率比较低，平衡考虑各因子
            score = (0.35 * profit_rate_score +
                    0.3 * closeness_score +
                    0.25 * volatility_score +
                    0.1 * diff_score)
        else:
            # 两方盈亏率都不低，强调差异度和波动率
            score = (0.2 * profit_rate_score +
                    0.2 * closeness_score +
                    0.35 * volatility_score +
                    0.25 * diff_score)
        
        # 同边风险等级判断（使用较低阈值）
        if score >= 0.6:
            risk_level = "高风险对敲"
        elif score >= 0.45:
            risk_level = "中等风险对敲"
        elif score >= 0.3:
            risk_level = "低风险对敲"
        else:
            risk_level = "正常交易"
    
    return score, risk_level


# ==================== 使用示例 ====================
if __name__ == "__main__":
    # 测试用例
    test_cases = [
        # (profit_a, profit_b, order_a, order_b, 描述)
        (-0.03, -1.0, 100, 100, "经典同边对敲：一方接近0"),
        (10, -9.5, 100, 100, "经典异边对敲：接近抵消"),
        (-5, -8, 100, 100, "正常同边交易：都有正常亏损"),
        (-0.1, -5, 1000, 1000, "大额同边对敲"),
        (15, 20, 100, 100, "正常同边盈利"),
    ]
    
    print("对敲检测算法测试结果：")
    print("-" * 60)
    
    for profit_a, profit_b, order_a, order_b, desc in test_cases:
        score, risk_level = calculate_hedge_score(profit_a, profit_b, order_a, order_b)
        print(f"{desc}")
        print(f"  盈亏: {profit_a:+.2f} / {profit_b:+.2f}  开单: {order_a} / {order_b}")
        print(f"  评分: {score:.3f}  等级: {risk_level}")
        print()
