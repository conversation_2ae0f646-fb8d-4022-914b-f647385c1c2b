#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化对敲检测算法 - 基于相对比例的多因子检测
"""

def calculate_hedge_score(profit_a, profit_b, order_amount_a, order_amount_b):
    """
    优化的对敲检测算法
    
    Args:
        profit_a (float): 仓位A的实际盈亏
        profit_b (float): 仓位B的实际盈亏  
        order_amount_a (float): 仓位A的开单金额（真实交易量）
        order_amount_b (float): 仓位B的开单金额（真实交易量）
    
    Returns:
        float: 0.0-1.0，越高越可能是对敲
    """
    
    # 基础检查
    total_profit = profit_a + profit_b
    profit_sum = abs(profit_a) + abs(profit_b)
    order_sum = order_amount_a + order_amount_b
    
    # 特殊情况：总盈亏为0（完全抵消，包括无盈亏情况）
    if abs(total_profit) == 0:
        return 0.8  # 给予较高分数，这也是对敲特征
    
    # 无交易量情况
    if order_sum == 0:
        return 0.0
    
    # 判断是否同边
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    
    if not is_same_side:
        # ==================== 异边情况：提高判断阈值 ====================
        score = 1.0 - abs(total_profit) / profit_sum
        # 提高阈值，减少误报
        return max(0, (score - 0.2) / 0.8)  # 将[0.2,1.0]映射到[0,1.0]
    
    else:
        # ==================== 同边情况：多因子相对比例检测 ====================
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        max_abs = max(abs_a, abs_b)
        
        # 使用真实交易量作为规模
        scale = order_sum
        
        # ---------- 因子1：接近0程度评分（基于相对比例） ----------
        min_relative = min_abs / scale
        if min_relative <= 0.001:      # ≤0.1%，几乎无盈亏
            closeness_score = 0.95
        elif min_relative <= 0.003:    # ≤0.3%，很接近0
            closeness_score = 0.9
        elif min_relative <= 0.005:    # ≤0.5%，接近0
            closeness_score = 0.8
        elif min_relative <= 0.01:     # ≤1%，比较接近0
            closeness_score = 0.6
        elif min_relative <= 0.02:     # ≤2%，稍微接近0
            closeness_score = 0.4
        elif min_relative <= 0.05:     # ≤5%，不太接近0
            closeness_score = 0.2
        else:                          # >5%，远离0
            closeness_score = 0.1
        
        # ---------- 因子2：相对波动率评分 ----------
        max_relative = max(abs_a / scale, abs_b / scale)
        if max_relative <= 0.01:       # ≤1%，波动很小
            volatility_score = 0.9
        elif max_relative <= 0.03:     # ≤3%，波动较小
            volatility_score = 0.8
        elif max_relative <= 0.05:     # ≤5%，波动中等
            volatility_score = 0.6
        elif max_relative <= 0.1:      # ≤10%，波动较大
            volatility_score = 0.4
        elif max_relative <= 0.2:      # ≤20%，波动很大
            volatility_score = 0.2
        else:                          # >20%，波动极大
            volatility_score = 0.1
        
        # ---------- 因子3：盈亏率差异评分（新增） ----------
        # 对敲特征：一方盈亏率很小，另一方盈亏率正常
        profit_rate_a = abs_a / order_amount_a if order_amount_a > 0 else 0
        profit_rate_b = abs_b / order_amount_b if order_amount_b > 0 else 0
        
        min_profit_rate = min(profit_rate_a, profit_rate_b)
        max_profit_rate = max(profit_rate_a, profit_rate_b)
        
        # 计算盈亏率差异
        if max_profit_rate > 0:
            rate_diff_ratio = (max_profit_rate - min_profit_rate) / max_profit_rate
        else:
            rate_diff_ratio = 0
        
        # 理想的对敲模式：盈亏率差异很大
        if min_profit_rate <= 0.005 and max_profit_rate >= 0.02:    # 一方≤0.5%，另一方≥2%
            rate_diff_score = 0.95
        elif min_profit_rate <= 0.01 and max_profit_rate >= 0.015:  # 一方≤1%，另一方≥1.5%
            rate_diff_score = 0.9
        elif min_profit_rate <= 0.02 and max_profit_rate >= 0.03:   # 一方≤2%，另一方≥3%
            rate_diff_score = 0.8
        elif rate_diff_ratio >= 0.8:                                # 差异比例≥80%
            rate_diff_score = 0.7
        elif rate_diff_ratio >= 0.6:                                # 差异比例≥60%
            rate_diff_score = 0.5
        elif rate_diff_ratio >= 0.4:                                # 差异比例≥40%
            rate_diff_score = 0.3
        else:                                                        # 差异很小
            rate_diff_score = 0.1
        
        # ---------- 多因子加权计算 ----------
        # 根据最小盈亏率动态调整权重
        if min_profit_rate <= 0.005:
            # 确实有一方盈亏率很低，强调盈亏率差异
            score = (0.5 * rate_diff_score +
                    0.3 * closeness_score +
                    0.2 * volatility_score)
        elif min_profit_rate <= 0.02:
            # 一方盈亏率比较低，平衡考虑各因子
            score = (0.4 * rate_diff_score +
                    0.3 * closeness_score +
                    0.3 * volatility_score)
        else:
            # 两方盈亏率都不低，主要看相对差异
            score = (0.3 * rate_diff_score +
                    0.2 * closeness_score +
                    0.5 * volatility_score)
        
        return score


# ==================== 使用示例 ====================
if __name__ == "__main__":
    # 测试用例
    test_cases = [
        # (profit_a, profit_b, order_a, order_b, 描述)
        (-0.03, -1.0, 100, 100, "同边对敲：一方接近0"),
        (10, -9.5, 100, 100, "异边对敲：接近抵消"),
        (-5, -8, 100, 100, "正常同边交易：都有正常亏损"),
        (-0.1, -5, 1000, 1000, "大额同边对敲"),
        (15, 20, 100, 100, "正常同边盈利"),
        (5, -5, 100, 100, "完全抵消：总盈亏为0"),
        (0, 0, 100, 100, "无盈亏：都为0"),
        # 新增大额测试
        (-2500, -50000, 500000, 500000, "大额同边对敲：一方0.5%，另一方10%"),
        (1000, -50000, 100000, 500000, "大额异边：不完全抵消"),
    ]
    
    print("优化对敲检测算法测试结果：")
    print("-" * 60)
    
    for profit_a, profit_b, order_a, order_b, desc in test_cases:
        score = calculate_hedge_score(profit_a, profit_b, order_a, order_b)
        print(f"{desc}")
        print(f"  盈亏: {profit_a:+.2f} / {profit_b:+.2f}")
        print(f"  开单: {order_a} / {order_b}")
        print(f"  评分: {score:.3f}")
        print()
