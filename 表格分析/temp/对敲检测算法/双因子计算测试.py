#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双因子对敲检测算法计算过程测试
"""

from 双因子对敲检测算法 import calculate_hedge_score

def detailed_test(profit_a, profit_b, order_amount_a, order_amount_b, description):
    """
    详细测试单个案例的计算过程
    """
    print(f"\n{'='*60}")
    print(f"测试案例: {description}")
    print(f"{'='*60}")
    
    # 基础数据
    print(f"输入数据:")
    print(f"  仓位A: 盈亏={profit_a:+.2f}, 开单金额={order_amount_a}")
    print(f"  仓位B: 盈亏={profit_b:+.2f}, 开单金额={order_amount_b}")
    
    # 计算过程
    total_profit = profit_a + profit_b
    profit_sum = abs(profit_a) + abs(profit_b)
    order_sum = order_amount_a + order_amount_b
    
    print(f"\n基础计算:")
    print(f"  总盈亏: {total_profit:+.2f}")
    print(f"  盈亏绝对值之和: {profit_sum:.2f}")
    print(f"  交易规模总和: {order_sum}")
    
    # 特殊情况检查
    if abs(total_profit) == 0:
        print(f"\n特殊情况: 总盈亏为0，直接返回0.8")
        return 0.8
    
    # 同边/异边判断
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    print(f"\n边向判断: {'同边' if is_same_side else '异边'}")
    
    if not is_same_side:
        # 异边情况
        score = 1.0 - abs(total_profit) / profit_sum
        adjusted_score = max(0, (score - 0.2) / 0.8)
        print(f"\n异边计算:")
        print(f"  原始评分: 1 - |{total_profit}| / {profit_sum} = {score:.4f}")
        print(f"  调整后评分: max(0, ({score:.4f} - 0.2) / 0.8) = {adjusted_score:.4f}")
        return adjusted_score
    
    else:
        # 同边情况 - 双因子计算
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        scale = order_sum
        
        print(f"\n同边双因子计算:")
        print(f"  |盈亏A|: {abs_a:.2f}")
        print(f"  |盈亏B|: {abs_b:.2f}")
        print(f"  最小绝对盈亏: {min_abs:.2f}")
        print(f"  交易规模: {scale}")
        
        # 因子1：接近0程度评分
        min_relative = min_abs / scale
        print(f"\n因子1 - 接近0程度:")
        print(f"  最小相对值: {min_abs} / {scale} = {min_relative:.6f} ({min_relative*100:.4f}%)")
        
        if min_relative <= 0.001:
            closeness_score = 0.95
            threshold_desc = "≤0.1%，几乎无盈亏"
        elif min_relative <= 0.003:
            closeness_score = 0.9
            threshold_desc = "≤0.3%，很接近0"
        elif min_relative <= 0.005:
            closeness_score = 0.8
            threshold_desc = "≤0.5%，接近0"
        elif min_relative <= 0.01:
            closeness_score = 0.6
            threshold_desc = "≤1%，比较接近0"
        elif min_relative <= 0.02:
            closeness_score = 0.4
            threshold_desc = "≤2%，稍微接近0"
        elif min_relative <= 0.05:
            closeness_score = 0.2
            threshold_desc = "≤5%，不太接近0"
        else:
            closeness_score = 0.1
            threshold_desc = ">5%，远离0"
        
        print(f"  判断: {threshold_desc}")
        print(f"  接近0评分: {closeness_score}")
        
        # 因子2：波动率比值评分
        rate_a = abs_a / order_amount_a if order_amount_a > 0 else 0
        rate_b = abs_b / order_amount_b if order_amount_b > 0 else 0
        min_rate = min(rate_a, rate_b)
        max_rate = max(rate_a, rate_b)
        
        print(f"\n因子2 - 波动率比值:")
        print(f"  仓位A盈亏率: {abs_a} / {order_amount_a} = {rate_a:.6f} ({rate_a*100:.4f}%)")
        print(f"  仓位B盈亏率: {abs_b} / {order_amount_b} = {rate_b:.6f} ({rate_b*100:.4f}%)")
        print(f"  最小盈亏率: {min_rate:.6f}")
        print(f"  最大盈亏率: {max_rate:.6f}")
        
        if max_rate > 0:
            rate_ratio = min_rate / max_rate
            print(f"  比值: {min_rate:.6f} / {max_rate:.6f} = {rate_ratio:.6f}")
            
            if rate_ratio <= 0.1:
                volatility_score = 0.9
                ratio_desc = "≤0.1，差异巨大"
            elif rate_ratio <= 0.2:
                volatility_score = 0.8
                ratio_desc = "≤0.2，差异很大"
            elif rate_ratio <= 0.5:
                volatility_score = 0.6
                ratio_desc = "≤0.5，差异较大"
            elif rate_ratio <= 0.8:
                volatility_score = 0.4
                ratio_desc = "≤0.8，差异一般"
            else:
                volatility_score = 0.2
                ratio_desc = ">0.8，差异很小"
        else:
            rate_ratio = 1.0
            volatility_score = 0.5
            ratio_desc = "最大盈亏率为0"
        
        print(f"  判断: {ratio_desc}")
        print(f"  波动率评分: {volatility_score}")
        
        # 最终评分
        final_score = 0.6 * closeness_score + 0.4 * volatility_score
        print(f"\n最终评分计算:")
        print(f"  评分 = 0.6 × {closeness_score} + 0.4 × {volatility_score}")
        print(f"       = {0.6 * closeness_score:.3f} + {0.4 * volatility_score:.3f}")
        print(f"       = {final_score:.4f}")
        
        return final_score

def run_tests():
    """运行多个测试案例"""
    test_cases = [
        # (profit_a, profit_b, order_a, order_b, 描述)
        (-0.1, -10, 100, 100, "典型同边对敲：0.1% vs 10%"),
        (-1, -2, 100, 100, "同边小差异：1% vs 2%"),
        (-5, -8, 100, 100, "正常同边交易：5% vs 8%"),
        (10, -9.5, 100, 100, "异边接近抵消"),
        (5, -5, 100, 100, "完全抵消"),
        (-2500, -50000, 500000, 500000, "大额同边对敲：0.5% vs 10%"),
    ]
    
    print("双因子对敲检测算法 - 详细计算过程测试")
    
    for profit_a, profit_b, order_a, order_b, desc in test_cases:
        calculated_score = detailed_test(profit_a, profit_b, order_a, order_b, desc)
        api_score = calculate_hedge_score(profit_a, profit_b, order_a, order_b)
        
        print(f"\n验证:")
        print(f"  手工计算评分: {calculated_score:.4f}")
        print(f"  API返回评分: {api_score:.4f}")
        print(f"  是否一致: {'✅' if abs(calculated_score - api_score) < 0.0001 else '❌'}")

if __name__ == "__main__":
    run_tests()
