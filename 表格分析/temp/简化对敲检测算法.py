#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化对敲检测算法 - 基于真实交易量
"""

def calculate_hedge_score(profit_a, profit_b, order_amount_a, order_amount_b):
    """
    对敲检测算法
    
    Args:
        profit_a (float): 仓位A的实际盈亏
        profit_b (float): 仓位B的实际盈亏  
        order_amount_a (float): 仓位A的开单金额（真实交易量）
        order_amount_b (float): 仓位B的开单金额（真实交易量）
    
    Returns:
        float: 0.0-1.0，越高越可能是对敲
    """
    
    # 基础检查
    total_profit = profit_a + profit_b
    profit_sum = abs(profit_a) + abs(profit_b)

    # 特殊情况：总盈亏为0（完全抵消，包括无盈亏情况）
    if abs(total_profit) == 0:
        return 0.8  # 给予较高分数，这也是对敲特征

    # 判断是否同边
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)

    if not is_same_side:
        # 异边（一盈一亏）：使用原始公式
        return 1.0 - abs(total_profit) / profit_sum
    
    else:
        # 同边（双盈/双亏）：优化后的接近0 + 相对波动率
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        max_abs = max(abs_a, abs_b)
        
        # 使用真实交易量作为规模
        scale = order_amount_a + order_amount_b
        
        # 优化的接近0程度评分（放宽阈值）
        if min_abs <= 0.2:  # 从0.1放宽到0.2
            closeness_score = 0.9
        elif min_abs <= 2:   # 从1放宽到2
            closeness_score = 0.8
        elif min_abs <= 8:   # 从5放宽到8
            closeness_score = 0.6
        elif min_abs <= 15:  # 从10放宽到15
            closeness_score = 0.4
        elif min_abs <= 30:  # 从20放宽到30
            closeness_score = 0.2
        else:
            closeness_score = 0.1
        
        # 相对波动率评分（保持不变）
        max_relative = max(abs_a / scale, abs_b / scale)
        if max_relative <= 0.01:
            volatility_score = 0.9
        elif max_relative <= 0.05:
            volatility_score = 0.8
        elif max_relative <= 0.1:
            volatility_score = 0.6
        elif max_relative <= 0.2:
            volatility_score = 0.4
        else:
            volatility_score = 0.2
        
        # 优化的权重：接近0(60%) + 相对波动率(40%)
        return 0.6 * closeness_score + 0.4 * volatility_score


# ==================== 使用示例 ====================
if __name__ == "__main__":
    # 测试用例
    test_cases = [
        # (profit_a, profit_b, order_a, order_b, 描述)
        (-0.03, -1.0, 100, 100, "同边对敲：一方接近0"),
        (10, -9.5, 100, 100, "异边对敲：接近抵消"),
        (-5, -8, 100, 100, "正常同边交易：都有正常亏损"),
        (-0.1, -5, 1000, 1000, "大额同边对敲"),
        (15, 20, 100, 100, "正常同边盈利"),
        (5, -5, 100, 100, "完全抵消：总盈亏为0"),
        (0, 0, 100, 100, "无盈亏：都为0"),
    ]
    
    print("对敲检测算法测试结果：")
    print("-" * 50)
    
    for profit_a, profit_b, order_a, order_b, desc in test_cases:
        score = calculate_hedge_score(profit_a, profit_b, order_a, order_b)
        print(f"{desc}")
        print(f"  盈亏: {profit_a:+.2f} / {profit_b:+.2f}")
        print(f"  开单: {order_a} / {order_b}")
        print(f"  评分: {score:.3f}")
        print()
